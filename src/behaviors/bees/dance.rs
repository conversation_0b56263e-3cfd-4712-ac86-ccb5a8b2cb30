use crate::components::animals::bees::{Bee, HomeBeeHive};
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::*;
use rand::Rng;

#[derive(Component, Reflect, Default, Clone)]
#[reflect(Component)]
pub struct DanceBehavior {
    pub dance_time: f32,
    pub max_dance_time: f32,
    pub dance_intensity: f32,
    pub dance_phase: f32,
}

impl DanceBehavior {
    pub fn new(max_dance_time: f32) -> Self {
        Self {
            dance_time: 0.0,
            max_dance_time,
            dance_intensity: rand::thread_rng().gen_range(0.5..1.0),
            dance_phase: 0.0,
        }
    }
}

pub fn dance_behavior(
    mut commands: Commands,
    mut dance_tasks: Query<(&mut DanceBehavior, &BehaveCtx)>,
    mut bee_query: Query<(
        &mut Transform,
        &mut LinearVelocity,
        &mut AngularVelocity,
        &HomeBeeHive,
        &mut Bee,
    )>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut dance, ctx) in dance_tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            home_hive,
            mut bee,
        )) = bee_query.get_mut(target_entity)
        {
            // Increment dance time
            dance.dance_time += delta_time.0;
            dance.dance_phase += delta_time.0 * 5.0 * dance.dance_intensity;

            // Calculate dance movement
            // Figure-eight pattern
            let x_offset = (dance.dance_phase * 0.5).sin() * 0.3 * dance.dance_intensity;
            let z_offset = (dance.dance_phase).sin() * 0.3 * dance.dance_intensity;

            // Apply dance movement
            linear_velocity.0 = Vec3::new(x_offset, 0.0, z_offset);

            // Apply rotation - bees spin while dancing
            angular_velocity.0 = Vec3::new(0.0, dance.dance_phase.sin() * 2.0, 0.0);

            // If we've danced long enough, report success
            if dance.dance_time >= dance.max_dance_time {
                // Reset velocity and angular velocity
                linear_velocity.0 = Vec3::ZERO;
                angular_velocity.0 = Vec3::ZERO;

                commands.trigger(ctx.success());
            }
        }
    }
}
