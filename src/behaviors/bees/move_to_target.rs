use crate::components::animals::AnimalMovementBehavior;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::math::Vec3;
use bevy::prelude::{
    Commands, Component, Entity, GlobalTransform, Query, Res, Transform, With
};
use bevy_behave::prelude::BehaveCtx;

pub fn move_to_target<Animal, Behavior, Target, Required>(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &Behavior)>,
    mut organism_query: Query<
        (&mut Animal, &mut Transform, &mut LinearVelocity, &mut AngularVelocity, &Target),
        With<Required>,
    >,
    target_query: Query<&GlobalTransform>,
    delta_time: Res<WorldSimulationDeltaTime>,
) where
    Animal:
        AnimalMovementBehavior + Component<Mutability = bevy::ecs::component::Mutable>,
    Behavior: Component,
    Required: Component,
    Target: Component + Into<Entity> + Copy,
{
    for (ctx, _behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut bee,
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            target,
        )) = organism_query.get_mut(target_entity)
        {
            // Get target transform
            if let Ok(target_transform) = target_query.get((*target).into()) {
                let target_pos = target_transform.translation();

                // Calculate velocity to target
                let (desired_velocity, target_direction) = bee
                    .calculate_velocity_towards_target(
                        delta_time.0,
                        target_pos,
                        transform.translation,
                    );

                linear_velocity.0 = desired_velocity;
                angular_velocity.0 = Vec3::ZERO;
                transform.look_to(target_direction, Vec3::Y);

                let distance = transform.translation.distance(target_pos);

                if distance <= bee.get_target_distance() {
                    commands.trigger(ctx.success());
                    continue;
                }
            } else {
                // Target no longer exists
                commands.entity(target_entity).remove::<Target>();
                commands.trigger(ctx.failure());
            }
        } else {
            // Missing required components
            commands.trigger(ctx.failure());
        }
    }
}
