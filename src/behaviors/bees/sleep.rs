use crate::components::animals::bees::{<PERSON>, <PERSON>Hive, HomeBeeHive};
use crate::components::animals::AnimalMovementBehavior;
use crate::components::lifecycle::{Hunger, Stamina, Thirst};
use crate::resources::{DayPhase, TimeOfDay, WorldSimulationDeltaTime};
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::BehaveCtx;

#[derive(Reflect, Component, Clone, Default)]
#[reflect(Component)]
pub(super) struct SleepBehavior {
    pub min_sleep_time: f32,
    sleep_time: f32,
    move_points: Vec<Vec3>,
    move_point_target_index: usize,
    reached_hive: bool,
}

impl SleepBehavior {
    pub fn new(min_sleep_time: f32) -> Self {
        Self {
            min_sleep_time,
            ..default()
        }
    }
}

/// System to handle bee sleep behavior
pub fn sleep_behavior(
    mut commands: Commands,
    mut sleep_tasks: Query<(&mut SleepBehavior, &BehaveCtx)>,
    mut bee_query: Query<(
        &mut Bee,
        &mut Transform,
        &HomeBeeHive,
        &mut LinearVelocity,
        &mut AngularVelocity,
    )>,
    mut stamina_query: Query<&mut Stamina>,
    mut hive_query: Query<(&GlobalTransform, &mut BeeHive)>,
    mut hunger_and_thirst_query: Query<(&mut Hunger, &mut Thirst)>,
    time_of_day: Res<TimeOfDay>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut sleep, ctx) in sleep_tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut bee,
            mut bee_transform,
            home_hive,
            mut velocity,
            mut angular_velocity,
        )) = bee_query.get_mut(target_entity)
        {
            let hive_entity = home_hive.0;

            if let Ok((hive_transform, mut hive)) = hive_query.get_mut(hive_entity) {
                let hive_pos = hive_transform.translation();

                // Calculate distance to hive
                let distance = bee_transform.translation.distance(hive_pos);

                // log::info!("Need sleep. Distance to hive: {} | Position: {:?} | Bee Position: {:?}", distance, hive_pos, bee_transform
                //     .translation);

                // If we're not at the hive yet, move towards it
                if distance > bee.get_target_distance() && !sleep.reached_hive {
                    // Move towards the hive
                    let (desired_velocity, target_direction) = bee
                        .calculate_velocity_towards_target(
                            delta_time.0,
                            hive_pos,
                            bee_transform.translation,
                        );

                    // Set the bee's velocity
                    velocity.0 = desired_velocity;
                    angular_velocity.0 = Vec3::ZERO;
                    bee_transform.look_to(target_direction, Vec3::Y);

                    // Deplete stamina while moving
                    if let Ok(mut stamina) = stamina_query.get_mut(target_entity) {
                        stamina.deplete(delta_time.0);
                    }
                } else {
                    // We're at the hive, now rest
                    sleep.reached_hive = true;

                    // Have the bees fly around the hive
                    // Choose random targets within a radius of the hive
                    if sleep.move_points.is_empty() {
                        sleep.move_points = (0..10)
                            .map(|_| {
                                let angle =
                                    rand::random::<f32>() * 2.0 * std::f32::consts::PI;

                                let radius = rand::random::<f32>() * 0.35;
                                let x = radius * angle.cos();
                                let y = rand::random::<f32>() * 0.15 * angle.cos();
                                let z = radius * angle.sin();
                                hive_transform.translation() + Vec3::new(x, y, z)
                            })
                            .collect::<Vec<_>>();
                    }

                    // Move to the targets
                    if let Some(target) =
                        sleep.move_points.get(sleep.move_point_target_index)
                    {
                        let (desired_velocity, target_direction) = bee
                            .calculate_velocity_towards_target_with_speed(
                                delta_time.0,
                                *target,
                                bee_transform.translation,
                                0.15,
                            );

                        // Set the bee's velocity
                        velocity.0 = desired_velocity;
                        angular_velocity.0 = Vec3::ZERO;
                        bee_transform.look_to(target_direction, Vec3::Y);

                        // Check if we've reached the target
                        let distance = bee_transform.translation.distance(*target);
                        if distance < 0.1 {
                            sleep.move_point_target_index =
                                (sleep.move_point_target_index + 1)
                                    % sleep.move_points.len();
                        }
                    }

                    // Regenerate stamina while sleeping
                    if let Ok(mut stamina) = stamina_query.get_mut(target_entity) {
                        stamina.regenerate(delta_time.0 * 2.0); // Regenerate faster while sleeping
                    }

                    // Track how long we've been sleeping
                    sleep.sleep_time += delta_time.0;

                    // Store any remaining nectar in the hive
                    hive.nectar_stored += bee.nectar_carried;
                    hive.nectar_stored = hive.nectar_stored.min(hive.max_nectar);
                    bee.nectar_carried = 0.0;
                    bee.reset_noise();

                    // Get a slight amount of water from the hive and some food
                    if let Ok((mut hunger, mut thirst)) =
                        hunger_and_thirst_query.get_mut(target_entity)
                    {
                        hunger.consume_food(0.01);
                        thirst.drink(0.001);
                    }

                    // Check if it's morning and we've slept enough
                    let is_morning = matches!(time_of_day.phase(), DayPhase::Morning);
                    let slept_enough = sleep.sleep_time >= sleep.min_sleep_time;
                    let fully_rested =
                        if let Ok(stamina) = stamina_query.get(target_entity) {
                            stamina.normalized_stamina() > 0.9 // 90% stamina is considered fully rested
                        } else {
                            false
                        };

                    // Wake up if it's morning and we've slept enough or we're fully rested
                    if (is_morning && slept_enough) || fully_rested {
                        // Reset sleep time
                        sleep.sleep_time = 0.0;

                        commands.trigger(ctx.success());
                        return;
                    }
                }
            } else {
                // Couldn't find the hive, report failure
                commands.trigger(ctx.failure());
            }
        } else {
            // Couldn't find the bee, report failure
            commands.trigger(ctx.failure());
        }
    }
}
