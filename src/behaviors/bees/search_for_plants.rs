use crate::behaviors::bees::PlantTarget;
use crate::components::animals::bees::Bee;
use crate::components::animals::LastVisitedPlants;
use crate::components::lifecycle::Stamina;
use crate::components::perception::{
    AIPerception, DetectedStimulusTarget, MemoryCategory
};
use crate::components::plants::{
    AnimalPollinatedPlant, Plant, PlantMatured, WindPollinatedPlant
};
use crate::resources::{TimeOfDay, WorldSimulationDeltaTime};
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::BehaveCtx;
use rand::Rng;

#[derive(Reflect, Default, Component, Clone)]
#[reflect(Component)]
pub(super) struct SearchForPlantBehavior {
    target: Option<Vec3>,
    time: f32,
    max_time: f32,
}

impl SearchForPlantBehavior {
    pub fn new(max_time: f32) -> Self {
        Self {
            max_time,
            ..default()
        }
    }
}

pub fn search_for_plants_behavior(
    mut commands: Commands,
    mut tasks: Query<
        (&BehaveCtx, &mut SearchForPlantBehavior),
        With<SearchForPlantBehavior>,
    >,
    mut bee_query: Query<(
        &mut Transform,
        &mut LinearVelocity,
        &mut AngularVelocity,
        &AIPerception,
        &mut Bee,
        &LastVisitedPlants,
        Option<&DetectedStimulusTarget>,
    )>,
    mut stamina_query: Query<&mut Stamina>,
    plant_query: Query<
        Entity,
        (
            With<Plant>,
            With<PlantMatured>,
            Or<(With<AnimalPollinatedPlant>, With<WindPollinatedPlant>)>,
        ),
    >,
    terrain_pod_system: TerrainSystemParams,
    delta_time: Res<WorldSimulationDeltaTime>,
    time_of_day: Res<TimeOfDay>,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            perception,
            mut bee,
            last_visited_plants,
            last_detected_target,
        )) = bee_query.get_mut(target_entity)
        {
            behavior.time += delta_time.0;

            if behavior.target.is_none() {
                behavior.target = Some(
                    terrain_pod_system
                        .get_random_position_in_pod_with_height_range(0.6, 1.0),
                );
            }

            let (desired_velocity, target_direction) = bee
                .calculate_velocity_towards_target(
                    delta_time.0,
                    behavior.target.unwrap_or_default(),
                    transform.translation,
                );

            linear_velocity.0 = desired_velocity;
            angular_velocity.0 = Vec3::ZERO;
            transform.look_to(target_direction, Vec3::Y);

            let distance = transform
                .translation
                .distance(behavior.target.unwrap_or_default());

            if let Ok(mut stamina) = stamina_query.get_mut(target_entity) {
                stamina.deplete(delta_time.0);
            }

            if distance < 1.0 {
                behavior.target = None;
            }

            if behavior.time > behavior.max_time * rand::thread_rng().gen_range(0.75..1.)
            {
                // Check if we should look for a plant (the less nectar we have, the more likely we are to look)
                if rand::thread_rng().gen_bool(
                    (1. - (bee.nectar_carried.max(0.01) / bee.max_nectar)) as f64,
                ) {
                    // Check long term memory for the oldest plant
                    if rand::thread_rng().gen_bool(0.5) {
                        if let Some(memory) = perception
                            .long_term_memory_by_category_iter(MemoryCategory::Resource)
                            .min_by(|a, b| {
                                a.last_encounter_time
                                    .partial_cmp(&b.last_encounter_time)
                                    .unwrap()
                            })
                        {
                            if let Some(source_entity) = memory.stimulus.source_entity {
                                // Check if we've visited this plant recently
                                if let Some(last_visit_time) =
                                    last_visited_plants.last_visit_time(source_entity)
                                {
                                    if time_of_day.elapsed_seconds - last_visit_time
                                        < 120.0
                                    {
                                        continue;
                                    }
                                }

                                // Check if we already had this as our last target
                                if let Some(last_detected_target) = last_detected_target {
                                    if last_detected_target.stimulus.source_entity
                                        == Some(source_entity)
                                    {
                                        continue;
                                    }
                                }

                                if plant_query.get(source_entity).is_ok() {
                                    bee.noise_time = 0.0;
                                    behavior.target = None;
                                    commands
                                        .entity(target_entity)
                                        .remove::<PlantTarget>()
                                        .insert(DetectedStimulusTarget {
                                            stimulus: memory.stimulus.clone(),
                                        });

                                    commands.trigger(ctx.success());
                                    return;
                                }
                            }
                        }
                    }

                    // Check if we can detect any plants through perception
                    // Sort stimuli by distance
                    let mut detected_stimuli = perception.detected_stimuli.clone();
                    detected_stimuli.sort_by(|a, b| {
                        a.location
                            .distance(transform.translation)
                            .partial_cmp(&b.location.distance(transform.translation))
                            .unwrap()
                    });

                    for stimulus in detected_stimuli {
                        // If we detect a visual stimulus, and it's a plant, track it
                        let source_entity = stimulus.source_entity.unwrap();

                        // Check if we've visited this plant recently
                        if let Some(last_visit_time) =
                            last_visited_plants.last_visit_time(source_entity)
                        {
                            if time_of_day.elapsed_seconds - last_visit_time < 60.0 {
                                continue;
                            }
                        }

                        // Check if we already had this as our last target
                        if let Some(last_detected_target) = last_detected_target {
                            if last_detected_target.stimulus.source_entity
                                == Some(source_entity)
                            {
                                continue;
                            }
                        }

                        if plant_query.get(source_entity).is_ok() {
                            bee.noise_time = 0.0;
                            commands
                                .entity(target_entity)
                                .remove::<PlantTarget>()
                                .insert(DetectedStimulusTarget {
                                    stimulus: stimulus.clone(),
                                });

                            commands.trigger(ctx.success());
                            return;
                        }
                    }
                };

                bee.noise_time = 0.0;
                behavior.target = None;
                commands.trigger(ctx.success());
            }
        }
    }
}
