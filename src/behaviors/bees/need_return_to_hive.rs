use crate::components::animals::bees::Bee;
use bevy::prelude::*;
use bevy_behave::prelude::BehaveTrigger;
use rand::Rng;

#[derive(Clone)]
pub(super) struct NeedReturnToHive;

pub fn needs_return_to_hive(
    trigger: Trigger<BehaveTrigger<NeedReturnToHive>>,
    query: Query<&Bee>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok(bee) = query.get(ctx.target_entity()) {
        // Check if the bee has collected enough nectar to return to the hive
        // Using 90% of max capacity as the threshold
        if bee.nectar_carried >= bee.max_nectar * rand::thread_rng().gen_range(0.9..1.0) {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}
