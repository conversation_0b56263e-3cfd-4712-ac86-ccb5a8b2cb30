mod dance;
mod drink_nectar;
mod idle;
mod move_to_plant;
mod move_to_target;
mod need_return_to_hive;
mod need_sleep;
mod return_to_hive;
mod search_for_plants;
mod search_randomly;
mod sleep;

use crate::behaviors::bees::dance::{dance_behavior, DanceBehavior};
use crate::behaviors::bees::drink_nectar::{
    drink_nectar_behavior, needs_nectar, DrinkNectarBehavior
};
use crate::behaviors::bees::idle::{idle_behavior, IdleBehavior};
use crate::behaviors::bees::move_to_plant::{
    move_to_plant_behavior, MoveToPlantBehavior
};
use crate::behaviors::bees::move_to_target::move_to_target;
use crate::behaviors::bees::need_return_to_hive::{
    needs_return_to_hive, NeedReturnToHive
};
use crate::behaviors::bees::need_sleep::needs_sleep;
use crate::behaviors::bees::return_to_hive::{
    return_to_hive_behavior, ReturnToHiveBehavior
};
use crate::behaviors::bees::search_for_plants::{
    search_for_plants_behavior, SearchForPlantBehavior
};
use crate::behaviors::bees::search_randomly::{
    search_randomly_behavior, SearchRandomlyBehavior
};
use crate::behaviors::bees::sleep::{sleep_behavior, SleepBehavior};
use crate::behaviors::common::{
    base_behavior, create_food_seeking_behavior, create_water_seeking_behavior, DetectFoodBehavior, DrinkWaterBehavior, EatFoodBehavior, FoodSeekingBehaviorTreeParams, FoodSourceTarget, MoveToFoodBehavior, MoveToWaterBehavior, WaterSeekingBehaviorTreeParams, WaterSourceTarget
};
use crate::behaviors::neural::animals::bees::{
    neural_needs_dance, neural_needs_pollinate, neural_needs_return_to_hive, NeuralNeedDance, NeuralNeedPollinate, NeuralNeedReturnToHive
};
use crate::behaviors::neural::needs_behaviors::{
    NeuralNeedFood, NeuralNeedRest, NeuralNeedWater
};
use crate::components::animals::bees::{Bee, BeeHive, HomeBeeHive};
use crate::components::animals::{LastVisitedPlants, VisitedPlant};
use crate::components::ecosystem::FoodType;
use crate::components::lifecycle::{Hunger, Thirst};
use crate::plugins::animals::AnimalsPluginState;
use crate::systems::animals::bees;
use bevy::app::{App, FixedUpdate, Plugin};
use bevy::prelude::*;
use bevy_behave::prelude::{BehaveTimeout, Tree};
use bevy_behave::{behave, Behave};
use rand::Rng;

pub struct BeeBehaviorsPlugin;

impl Plugin for BeeBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app.register_type::<Bee>()
            .register_type::<BeeHive>()
            .register_type::<HomeBeeHive>()
            .register_type::<PlantTarget>()
            .register_type::<VisitedPlant>()
            .register_type::<LastVisitedPlants>()
            .register_type::<SleepBehavior>()
            .register_type::<SearchForPlantBehavior>()
            .register_type::<IdleBehavior>()
            .register_type::<DanceBehavior>()
            .add_observer(needs_nectar)
            .add_observer(needs_return_to_hive)
            .add_observer(needs_sleep)
            .add_observer(neural_needs_pollinate)
            .add_observer(neural_needs_dance)
            .add_observer(neural_needs_return_to_hive)
            .add_systems(
                FixedLast,
                (bees::spawn_bee_workers_from_bee_hive,)
                    .run_if(in_state(AnimalsPluginState::Done)),
            )
            .add_systems(
                FixedUpdate,
                (
                    idle_behavior,
                    move_to_target::<Bee, MoveToFoodBehavior, FoodSourceTarget, Hunger>,
                    move_to_target::<Bee, MoveToWaterBehavior, WaterSourceTarget, Thirst>,
                    return_to_hive_behavior,
                    sleep_behavior,
                    move_to_plant_behavior,
                    drink_nectar_behavior,
                    search_for_plants_behavior,
                    search_randomly_behavior,
                    dance_behavior,
                    bees::lose_nectar_system,
                    bees::spawn_bee_hive,
                    bees::clean_up_last_visited_plants,
                    bees::handle_bee_materials,
                    bees::setup_bee_animation_wings,
                    bees::produce_honey_for_bee_hive,
                )
                    .run_if(in_state(AnimalsPluginState::Done)),
            );
    }
}

#[derive(Reflect, Component, Debug, Clone)]
#[component(storage = "SparseSet")]
#[reflect(Component)]
pub struct PlantTarget(pub Entity);

#[derive(Clone)]
pub struct NeedSleep;

fn get_nectar_subtree(_bee: &Bee) -> Tree<Behave> {
    behave! {
        Behave::Sequence => {
            Behave::spawn_named("Search for Plant", SearchForPlantBehavior::new(
                10.0 + rand::thread_rng().gen_range(0.0..3.0)
            )),
            // Find and move to the plant
            Behave::Sequence => {
                Behave::spawn_named("Move To Plant", (MoveToPlantBehavior, BehaveTimeout::from_secs(50.0, false))),
                Behave::Wait(1.0 * rand::thread_rng().gen_range(0.5..1.5))
            },
            // Drink nectar from the plant
            Behave::Sequence => {
                Behave::spawn_named("Drink Nectar", DrinkNectarBehavior),
                Behave::Wait(5.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        }
    }
}

fn get_water_subtree(bee: &Bee) -> Tree<Behave> {
    create_water_seeking_behavior(WaterSeekingBehaviorTreeParams {
        drink_water_params: Some(DrinkWaterBehavior::new(
            rand::thread_rng().gen_range(3.0..6.0),
            || rand::thread_rng().gen_range(3.0..10.0),
        )),
        // Custom move to water tree that uses the bee's max speed
        move_to_water_tree: Some(behave! {
            Behave::spawn_named("Move To Water", (MoveToWaterBehavior {
                max_speed: bee.acceleration_speed,
                not_used_for_common_behavior: true,
                drinking_distance: 0.3,
                ..default()
            }, BehaveTimeout::from_secs(100.0, false))),
        }),
        // Move randomly in hopes to find water
        on_fail_to_detect_water_tree: Some(behave! {
            Behave::spawn_named("Search To Find Water", (
                SearchRandomlyBehavior::new(3.0),
                BehaveTimeout::from_secs(100.0, false)
            )),
        }),
        ..default()
    })
}

fn get_food_subtree(bee: &Bee) -> Tree<Behave> {
    create_food_seeking_behavior(FoodSeekingBehaviorTreeParams {
        eat_food_params: Some(EatFoodBehavior {
            eating_rate: 1.0,
            ..default()
        }),
        detect_food_params: Some(DetectFoodBehavior {
            search_time: 6.0,
            consumable_food_types: vec![
                FoodType::Nectar,
                FoodType::Sugar,
                FoodType::Pollen,
            ],
            ..default()
        }),
        // Custom move to food tree that uses the bee's max speed
        move_to_food_tree: Some(behave! {
            Behave::spawn_named("Move To Food", (MoveToFoodBehavior {
                max_speed: bee.acceleration_speed,
                not_used_for_common_behavior: true,
                ..default()
            }, BehaveTimeout::from_secs(20.0, false))),
        }),
        // Move randomly in hopes to find food
        on_fail_to_detect_food_tree: Some(behave! {
            Behave::spawn_named("Search To Find Food", SearchRandomlyBehavior::new(3.0)),
        }),
        ..default()
    })
}

pub fn create_neural_needs_behavior_tree2(bee: &Bee) -> Tree<Behave> {
    behave! {
        Behave::Forever => {
            Behave::Fallback => {
                Behave::While => {
                    Behave::trigger(NeuralNeedWater),
                    Behave::Sequence => {
                        @ get_water_subtree(&bee),
                        Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedFood),
                    Behave::Sequence => {
                        @ get_food_subtree(&bee),
                        Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedRest),
                    Behave::Sequence => {
                        Behave::spawn_named("Rest", (SleepBehavior::new(250.0), BehaveTimeout::from_secs(1000.0, false))),
                        Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                Behave::While => {
                    Behave::trigger(NeedReturnToHive),
                    Behave::Sequence => {
                        Behave::spawn_named("Return To Hive", (ReturnToHiveBehavior, BehaveTimeout::from_secs(100.0, false))),
                        Behave::Wait(5.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                @ get_nectar_subtree(&bee),
                Behave::spawn_named("Idle",
                    IdleBehavior {
                        max_idle_time: 0.5 * rand::thread_rng().gen_range(0.5..1.5),
                        ..default()
                    }
                )
            }
        }
    }
}

pub fn create_neural_needs_behavior_tree(bee: &Bee) -> Tree<Behave> {
    base_behavior(
        behave! {
            Behave::Sequence => {
                @ get_water_subtree(&bee),
                Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        },
        behave! {
            Behave::Sequence => {
                @ get_food_subtree(&bee),
                Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        },
        behave! {
            Behave::Sequence => {
                Behave::spawn_named("Rest", (SleepBehavior::new(250.0), BehaveTimeout::from_secs(1000.0, false))),
                Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        },
        behave! {
            Behave::Fallback => {
                // Bee-specific behaviors driven by neural network
                Behave::While => {
                    Behave::trigger(NeuralNeedReturnToHive),
                    Behave::Sequence => {
                        Behave::spawn_named("Return To Hive", (ReturnToHiveBehavior, BehaveTimeout::from_secs(100.0, false))),
                        Behave::Wait(5.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedPollinate),
                    Behave::Sequence => {
                        @ get_nectar_subtree(&bee),
                        Behave::Wait(6.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedDance),
                    Behave::Sequence => {
                        Behave::spawn_named("Dance", (DanceBehavior::new(5.0), BehaveTimeout::from_secs(50.0, false))),
                        Behave::Wait(2.0 * rand::thread_rng().gen_range(0.5..1.5))
                    }
                },
                // Default behavior when neural network doesn't trigger anything specific
                Behave::spawn_named("Move Randomly", (
                    SearchRandomlyBehavior::new(10.0 * rand::thread_rng().gen_range(0.5..1.5)),
                    BehaveTimeout::from_secs(15.1, false)
                )),
                Behave::spawn_named("Idle",
                    IdleBehavior {
                        max_idle_time: 0.5 * rand::thread_rng().gen_range(0.5..1.5),
                        ..default()
                    }
                )
            }
        },
    )
}
