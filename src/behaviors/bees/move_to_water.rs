use crate::behaviors::common::{MoveToWaterBehavior, WaterSourceTarget};
use crate::components::animals::bees::Bee;
use crate::components::lifecycle::Thirst;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::{
    Commands, GlobalTransform, Query, Res, Transform, Vec3, With
};
use bevy_behave::prelude::BehaveCtx;

/// System to handle moving to a water source
pub(super) fn move_to_water_behavior(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &MoveToWaterBehavior)>,
    mut organism_query: Query<
        (
            &mut Bee,
            &mut Transform,
            &mut LinearVelocity,
            &mut AngularVelocity,
            &WaterSourceTarget,
        ),
        With<Thirst>,
    >,
    water_query: Query<&GlobalTransform>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut bee,
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            water_target,
        )) = organism_query.get_mut(target_entity)
        {
            // Get water source transform
            if let Ok(water_transform) = water_query.get(water_target.0) {
                let water_pos = water_transform.translation();

                // Calculate velocity to water
                let (desired_velocity, target_direction) = bee
                    .calculate_velocity_towards_target(
                        delta_time.0,
                        water_pos,
                        transform.translation,
                    );

                linear_velocity.0 = desired_velocity;
                angular_velocity.0 = Vec3::ZERO;
                transform.look_to(target_direction, Vec3::Y);

                let distance = transform.translation.distance(water_pos);

                if distance <= behavior.drinking_distance {
                    commands.trigger(ctx.success());
                    continue;
                }
            } else {
                // Water source no longer exists
                commands.entity(target_entity).remove::<WaterSourceTarget>();
                commands.trigger(ctx.failure());
            }
        } else {
            // Missing required components
            commands.trigger(ctx.failure());
        }
    }
}