use crate::components::animals::bees::Bee;
use crate::components::lifecycle::Stamina;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::BehaveCtx;
use rand::Rng;

#[derive(Default, Component, Clone)]
pub(super) struct SearchRandomlyBehavior {
    target: Option<Vec3>,
    time: f32,
    pub max_time: f32,
}

impl SearchRandomlyBehavior {
    pub fn new(max_time: f32) -> Self {
        Self {
            max_time,
            ..default()
        }
    }
}

pub fn search_randomly_behavior(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut SearchRandomlyBehavior)>,
    mut organism_query: Query<(
        &mut Transform,
        &mut LinearVelocity,
        &mut AngularVelocity,
        &mut Bee,
    )>,
    mut stamina_query: Query<&mut Stamina>,
    delta_time: Res<WorldSimulationDeltaTime>,
    terrain_pod_system: TerrainSystemParams,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((mut transform, mut linear_velocity, mut angular_velocity, mut bee)) =
            organism_query.get_mut(target_entity)
        {
            behavior.time += delta_time.0;

            if behavior.target.is_none() {
                let target = terrain_pod_system
                    .get_random_position_in_pod_with_height_range(0.25, 1.5);

                // Ensure the target is above the terrain (grass)
                behavior.target = Some(target);
            }

            let target = behavior.target.unwrap_or_default();
            let mut speed = bee.acceleration_speed;

            // Have stamina affect speed after a threshold
            if let Ok(mut stamina) = stamina_query.get_mut(target_entity) {
                stamina.deplete(delta_time.0);

                if stamina.normalized_stamina() < 0.5 {
                    speed *= stamina.normalized_stamina();
                }
            }

            let (desired_velocity, target_direction) = bee
                .calculate_velocity_towards_target_with_speed(
                    delta_time.0,
                    target,
                    transform.translation,
                    speed,
                );

            linear_velocity.0 = desired_velocity;
            angular_velocity.0 = Vec3::ZERO;
            transform.look_to(target_direction, Vec3::Y);

            let distance = transform.translation.distance(target);

            if distance <= 0.1 {
                behavior.target = None;
            }

            if behavior.time > behavior.max_time * rand::thread_rng().gen_range(0.85..1.)
            {
                behavior.target = None;
                bee.noise_time = 0.0;
                commands.trigger(ctx.success());
                continue;
            }
        }
    }
}
