use crate::components::animals::deer::Deer;
use crate::components::animals::AnimalAnimationIndex;
use avian3d::prelude::LinearVelocity;
use bevy::prelude::*;
use bevy_gltf_animation::prelude::GltfAnimations;

pub fn deer_animation_system(
    mut query: Query<(&mut GltfAnimations, &mut LinearVelocity, &mut Deer)>,
    mut players: Query<&mut AnimationPlayer>,
) {
    for (mut gltf_animations, mut transform, deer) in query.iter_mut() {
        let Ok(mut player) = players.get_mut(gltf_animations.animation_player) else {
            continue;
        };

        // let Some(index) = gltf_animations.get("Animation 18") else {
        let Some(index) = gltf_animations.get(AnimalAnimationIndex::WALK) else {
            continue;
        };

        if player.is_playing_animation(index) {
            continue;
        }

        let mut animation = player.start(index);
        animation.repeat();
    }
}
