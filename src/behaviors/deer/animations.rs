// This file is deprecated - animation system has been moved to a generic system
// See src/systems/animals/animation_controller.rs for the new implementation

use crate::components::animals::deer::Deer;
use bevy::prelude::*;

/// Deprecated: This system is no longer used.
/// The generic animation system handles all organism animations now.
/// This function is kept for backward compatibility but does nothing.
pub fn deer_animation_system(
    _query: Query<Entity, With<Deer>>,
) {
    // This system is deprecated and replaced by the generic animation system
    // The generic system automatically handles all organisms with GltfAnimations
}
