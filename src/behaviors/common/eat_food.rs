use crate::behaviors::common::food_detection::FoodSourceTarget;
use crate::components::lifecycle::Hunger;
use crate::components::perception::{StimulusData, StimulusSources};
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;
use bevy_behave::prelude::*;

/// Behavior component for eating food
#[derive(Component, Clone)]
pub struct EatFoodBehavior {
    /// How much food to eat per second
    pub eating_rate: f32,
    /// How long to eat for
    pub eating_time: f32,
    /// Current eating time
    pub current_eating_time: f32,
}

impl Default for EatFoodBehavior {
    fn default() -> Self {
        Self {
            eating_rate: 10.0,
            eating_time: 2.0,
            current_eating_time: 0.0,
        }
    }
}

/// System to handle eating food
pub fn eat_food_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut EatFoodBehavior)>,
    mut organism_query: Query<(&mut Hunger, &FoodSourceTarget)>,
    food_query: Query<&StimulusSources>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((mut hunger, food_target)) = organism_query.get_mut(target_entity) {
            // Get food source
            if let Ok(stimulus_sources) = food_query.get(food_target.0) {
                if let Some(stimulus_source) = stimulus_sources
                    .iter()
                    .find(|s| matches!(s.data, StimulusData::Food { .. }))
                {
                    if let StimulusData::Food {
                        nutrition_value,
                        is_consumable,
                        ..
                    } = stimulus_source.data
                    {
                        if !is_consumable {
                            // This food source is not consumable
                            commands.trigger(ctx.failure());
                            continue;
                        }

                        // Increment eating time
                        behavior.current_eating_time += delta_time.0;

                        // Calculate how much food to eat this frame
                        let food_amount = behavior.eating_rate * delta_time.0;

                        // Eat food
                        hunger.consume_food(food_amount * nutrition_value);

                        // Check if we've eaten enough or for long enough
                        if behavior.current_eating_time >= behavior.eating_time
                            || hunger.current_hunger >= hunger.max_hunger
                        {
                            // We've eaten enough, reset eating time for next time
                            behavior.current_eating_time = 0.0;

                            // Remove the food target
                            commands.entity(target_entity).remove::<FoodSourceTarget>();

                            // Success!
                            commands.trigger(ctx.success());
                            return;
                        }

                        continue;
                    }
                }
            }
        }

        // If we get here, something went wrong
        commands.trigger(ctx.failure());
    }
}
