use crate::components::ecosystem::FoodType;
use crate::components::lifecycle::Hunger;
use crate::components::perception::{
    AIPerception, MemoryCategory, StimulusData, StimulusSources, StimulusType
};
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;
use bevy_behave::prelude::*;

/// Behavior component for detecting food sources
#[derive(Reflect, Component, Clone)]
#[reflect(Component)]
pub struct DetectFoodBehavior {
    /// How long to search for food before giving up
    pub search_time: f32,
    /// Current search time
    pub current_search_time: f32,
    pub consumable_food_types: Vec<FoodType>,
}

impl Default for DetectFoodBehavior {
    fn default() -> Self {
        Self {
            search_time: 5.0,
            current_search_time: 0.0,
            consumable_food_types: vec![
                FoodType::Plant,
                FoodType::Nectar,
                FoodType::Animal,
            ],
        }
    }
}

/// Component to store the detected food source
#[derive(<PERSON>flect, Component, Debug, Clone, Copy)]
#[reflect(Component)]
pub struct FoodSourceTarget(pub Entity);

impl Into<Entity> for FoodSourceTarget {
    fn into(self) -> Entity {
        self.0
    }
}

/// Trigger event for checking if organism needs food
#[derive(Clone)]
pub struct NeedFood;

/// System to check if an organism needs food
pub fn needs_food(
    trigger: Trigger<BehaveTrigger<NeedFood>>,
    query: Query<&Hunger>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok(hunger) = query.get(ctx.target_entity()) {
        if hunger.is_critical() || hunger.is_starving() {
            log::info!("Organism needs food");
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

/// System to detect food sources
pub fn detect_food_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut DetectFoodBehavior)>,
    organism_query: Query<(&Transform, &AIPerception, Option<&FoodSourceTarget>), With<Hunger>>,
    food_query: Query<(Entity, &Transform, &StimulusSources)>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((_transform, perception, food_target)) = organism_query.get(target_entity) {
            if food_target.is_some() {
                // We already have a food target, don't search again
                commands.trigger(ctx.success());
                continue;
            }

            // Increment search time
            behavior.current_search_time += delta_time.0;

            // Check if we've been searching too long
            if behavior.current_search_time > behavior.search_time {
                // Reset search time for next attempt
                behavior.current_search_time = 0.0;
                commands.trigger(ctx.failure());
                continue;
            }

            // First check if we already have a food source in memory
            let food_memory = perception
                .long_term_memory_iter()
                .filter(|m| m.category == MemoryCategory::Resource)
                .find(|m| {
                    if let StimulusData::Food {
                        is_consumable,
                        food_type,
                        ..
                    } = m.stimulus.data
                    {
                        is_consumable
                            && behavior.consumable_food_types.contains(&food_type)
                    } else {
                        false
                    }
                });

            if let Some(memory) = food_memory {
                if let Some(source_entity) = memory.stimulus.source_entity {
                    // Check if the food source still exists
                    if food_query.contains(source_entity) {
                        // Found a food source in memory
                        commands
                            .entity(target_entity)
                            .insert(FoodSourceTarget(source_entity));
                        commands.trigger(ctx.success());
                        return;
                    } else {
                        log::info!("Food source in memory no longer exists");
                    }
                }
            } else {
                log::info!("No food in memory");
            }

            // Check for food sources in short-term memory (recently detected)
            for stimulus in &perception.detected_stimuli {
                if let Some(source_entity) = stimulus.source_entity {
                    if let StimulusData::Food {
                        is_consumable,
                        food_type,
                        ..
                    } = &stimulus.data
                    {
                        if *is_consumable
                            && behavior.consumable_food_types.contains(&food_type)
                        {
                            // Found a food source in short-term memory
                            commands
                                .entity(target_entity)
                                .insert(FoodSourceTarget(source_entity));
                            commands.trigger(ctx.success());
                            return;
                        }
                    } else {
                        log::info!("Food source in short-term memory is not consumable: {:?}", stimulus.data);
                    }
                }
            }

            // If we haven't found anything in memory, look for nearby food sources
            // let mut closest_food = None;
            // let mut closest_distance = f32::MAX;

            // Check entities with StimulusSources
            // for (entity, food_transform, sources) in food_query.iter() {
            //     // Check each source in the collection
            //     for source in sources.iter() {
            //         if let StimulusData::Food { is_consumable, .. } = &source.data {
            //             if *is_consumable {
            //                 let distance = transform
            //                     .translation
            //                     .distance(food_transform.translation);
            //                 if distance < closest_distance {
            //                     closest_food = Some(entity);
            //                     closest_distance = distance;
            //                 }
            //                 // Once we find a food source in this entity, we can break
            //                 break;
            //             }
            //         }
            //     }
            // }

            // if let Some(food_entity) = closest_food {
            //     // Found a food source
            //     commands
            //         .entity(target_entity)
            //         .insert(FoodSourceTarget(food_entity));
            //     commands.trigger(ctx.success());
            //     return;
            // }
        }

        // If we get here, we didn't find any food
        commands.trigger(ctx.failure());
    }
}
