use crate::components::ai::neural_network::{BaseOutputType, NeuralBrain};
use crate::components::lifecycle::{Hunger, Stamina, Thirst};
use bevy::prelude::*;
use bevy_behave::prelude::*;

// Trigger for neural network-based water need detection
#[derive(Clone)]
pub struct NeuralNeedWater;

pub fn neural_needs_water(
    trigger: Trigger<BehaveTrigger<NeuralNeedWater>>,
    query: Query<(&NeuralBrain, &Thirst)>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, thirst)) = query.get(ctx.target_entity()) {
        // Neural network output for drinking water
        let Some(drink_decision) = brain.get_base_output(BaseOutputType::Drink) else {
            commands.trigger(ctx.failure());
            return;
        };

        // Proactive decision - neural network decides when to drink
        // Higher threshold for critical state
        if drink_decision > 0.6 || thirst.is_critical() {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

// Trigger for neural network-based food need detection
#[derive(Clone)]
pub struct NeuralNeedFood;

pub fn neural_needs_food(
    trigger: Trigger<BehaveTrigger<NeuralNeedFood>>,
    query: Query<(&NeuralBrain, &Hunger)>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, hunger)) = query.get(ctx.target_entity()) {
        // Neural network output for eating food
        let Some(eat_decision) = brain.get_base_output(BaseOutputType::Eat) else {
            commands.trigger(ctx.failure());
            return;
        };

        // Proactive decision - neural network decides when to eat
        // Higher threshold for critical state
        if eat_decision > 0.6 || hunger.is_critical() {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

// Trigger for neural network-based rest need detection
#[derive(Clone)]
pub struct NeuralNeedRest;

pub fn neural_needs_rest(
    trigger: Trigger<BehaveTrigger<NeuralNeedRest>>,
    query: Query<(&NeuralBrain, &Stamina)>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, stamina)) = query.get(ctx.target_entity()) {
        // Neural network output for resting
        let Some(rest_decision) = brain.get_base_output(BaseOutputType::Rest) else {
            commands.trigger(ctx.failure());
            return;
        };

        // Proactive decision - neural network decides when to rest
        if rest_decision > 0.6 || stamina.normalized_stamina() < 0.1 {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}
