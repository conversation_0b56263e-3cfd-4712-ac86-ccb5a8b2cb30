use crate::components::ai::neural_network::NeuralBrain;
use crate::components::animals::bees::{<PERSON>, HomeBeeHive};
use bevy::prelude::*;
use bevy_behave::prelude::BehaveTrigger;

// Trigger for neural network-based pollination need detection
#[derive(Clone)]
pub struct NeuralNeedPollinate;

pub fn neural_needs_pollinate(
    trigger: Trigger<BehaveTrigger<NeuralNeedPollinate>>,
    query: Query<(&NeuralBrain, &Bee)>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, bee)) = query.get(ctx.target_entity()) {
        // Neural network output for pollination
        let Some(pollinate_decision) = brain.get_extended_output("Pollinate") else {
            commands.trigger(ctx.failure());
            return;
        };

        // Get flower proximity from neural inputs
        let flower_proximity = brain.get_extended_input("FlowerProximity").unwrap_or(0.0);
        let pollen_load = brain.get_extended_input("PollenLoad").unwrap_or(0.0);

        // Trigger pollination when neural network decides to pollinate
        // and there are flowers nearby and bee isn't full of nectar
        if pollinate_decision > 0.6 && flower_proximity > 0.3 && pollen_load < 0.9 {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

// Trigger for neural network-based dance need detection
#[derive(Clone)]
pub struct NeuralNeedDance;

pub fn neural_needs_dance(
    trigger: Trigger<BehaveTrigger<NeuralNeedDance>>,
    query: Query<(&NeuralBrain, &Transform, &Bee, Option<&HomeBeeHive>)>,
    hive_query: Query<&GlobalTransform>,
    nearby_bees_query: Query<&GlobalTransform, With<Bee>>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, bee_transform, bee, home_hive)) = query.get(ctx.target_entity()) {
        // Neural network output for dancing
        let Some(dance_decision) = brain.get_extended_output("Dance") else {
            commands.trigger(ctx.failure());
            return;
        };

        // Only dance when near the hive and carrying good nectar
        let near_hive = if let Some(hive_ref) = home_hive {
            if let Ok(hive_transform) = hive_query.get(hive_ref.0) {
                // Check if bee is near its hive
                bee_transform
                    .translation
                    .distance(hive_transform.translation())
                    < 5.0
            } else {
                false
            }
        } else {
            false
        };

        // Check if there are other bees nearby
        let nearby_bees = nearby_bees_query
            .iter()
            .filter(|transform| {
                bee_transform.translation.distance(transform.translation()) < 6.0
            })
            .count();

        if nearby_bees < 1 {
            commands.trigger(ctx.failure());
            return;
        }

        let nectar_quality = brain.get_extended_input("NectarQuality").unwrap_or(0.0);

        // Trigger dance when neural network decides to dance
        // and bee is near hive with good quality nectar
        if dance_decision > 0.7 && near_hive && nectar_quality > 0.6 {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

// Trigger for neural network-based return to hive need detection
#[derive(Clone)]
pub struct NeuralNeedReturnToHive;

pub fn neural_needs_return_to_hive(
    trigger: Trigger<BehaveTrigger<NeuralNeedReturnToHive>>,
    query: Query<(&NeuralBrain, &Bee)>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok((brain, bee)) = query.get(ctx.target_entity()) {
        // Neural network output for returning to hive
        let Some(return_decision) = brain.get_extended_output("ReturnToHive") else {
            commands.trigger(ctx.failure());
            return;
        };

        // Get pollen load from neural inputs
        let pollen_load = brain.get_extended_input("PollenLoad").unwrap_or(0.0);

        // Trigger return to hive when neural network decides to return
        // or when bee is full of nectar
        if return_decision > 0.6 || pollen_load > 0.9 {
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}
