use crate::components::lifecycle::{
    BiologicalOrganism, CurrentTerrainPod, Dying, Hunger, Lifespan, Organism, Stamina, Thirst
};
use crate::events::life_cycle_events::{DeathCause, OrganismDied};
use crate::resources::environment::TerrainPodGrid;
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;

pub mod gender;

/// System to update entity lifespans and handle aging
pub fn update_lifespans(
    mut commands: Commands,
    mut query: Query<(Entity, &mut Lifespan, Option<&Dying>), With<Organism>>,
    mut organism_died_events: EventWriter<OrganismDied>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (entity, mut lifespan, dying) in query.iter_mut() {
        // Increment age
        lifespan.current_age += delta_time.0 / 0.5;

        // Health affects aging rate
        lifespan.current_age += delta_time.0 * (1.0 - lifespan.health);

        // Health decreases over time and is affect by current age
        let health_decline_rate = 0.00000001 * lifespan.current_age;
        lifespan.decrease_health(health_decline_rate);

        // Check if entity has reached end of life
        if lifespan.is_at_end_of_life() && dying.is_none() {
            log::info!("Lifespan: {lifespan:?} for {entity:?}");

            // Start the dying process
            commands.entity(entity).insert(Dying::default());
            organism_died_events.write(OrganismDied {
                entity,
                cause: DeathCause::OldAge,
            });

            log::info!(
                "Organism {entity:?} has reached the end of its lifespan and is dying."
            );
        }
    }
}

/// System to handle the dying process
pub fn handle_dying(
    mut commands: Commands,
    mut query: Query<(Entity, &mut Dying, Option<&mut Transform>), With<Organism>>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (entity, mut dying, transform) in query.iter_mut() {
        // Increment elapsed time
        dying.elapsed += delta_time.0;

        // Apply visual effects to dying entities
        if let Some(_transform) = transform {
            // Gradually sink into the ground or shrink
            let progress = dying.progress();
            // transform.scale = Vec3::splat(1.0 - (progress * 0.5));

            // Optionally, rotate slightly to look like it's falling over
            // Using its current rotation
            if progress > 0.5 {
                let tilt = (progress - 0.5) * 0.5;
                // transform.rotation *= Quat::from_rotation_x(tilt);
            }
        }

        // Check if dying process is complete
        if dying.is_complete() {
            log::info!("Entity {entity:?} has died and been removed from the world");

            // Remove the entity
            commands.entity(entity).despawn();
        }
    }
}

/// System to update the CurrentTerrainPod component when organisms move between pods
pub fn update_organism_terrain_pod(
    mut commands: Commands,
    mut query: Query<
        (Entity, &Transform, Option<&mut CurrentTerrainPod>),
        With<BiologicalOrganism>,
    >,
    pod_grid: Res<TerrainPodGrid>,
) {
    for (entity, transform, current_pod) in query.iter_mut() {
        // Get the world position of the organism
        let position = transform.translation;

        // Convert world position to pod grid coordinates
        let pod_index = pod_grid.world_to_cell(position);

        // Get the pod entity at these coordinates
        if let Some(&pod_entity) = pod_grid.get_pod(pod_index.0, pod_index.1) {
            match current_pod {
                // If the organism already has a CurrentTerrainPod component
                Some(mut current_pod) => {
                    // If the organism has moved to a different pod, update the component
                    if current_pod.pod_index != pod_index {
                        current_pod.pod_entity = pod_entity;
                        current_pod.pod_index = pod_index;
                        info!(
                            "Organism {:?} moved to pod ({}, {})",
                            entity, pod_index.0, pod_index.1
                        );
                    }
                }
                // If the organism doesn't have a CurrentTerrainPod component yet, add one
                None => {
                    commands
                        .entity(entity)
                        .insert(CurrentTerrainPod::new(pod_entity, pod_index));
                    info!(
                        "Assigned organism {:?} to pod ({}, {})",
                        entity, pod_index.0, pod_index.1
                    );
                }
            }
        }
    }
}

/// System to update organism stamina levels
pub fn update_stamina(
    mut query: Query<(&mut Stamina, Option<&Lifespan>), With<BiologicalOrganism>>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut stamina, lifespan) in query.iter_mut() {
        // Apply vitality modifier if lifespan component exists
        // Older organisms regenerate stamina more slowly
        if let Some(lifespan) = lifespan {
            let vitality = lifespan.vitality();

            // Adjust regeneration and depletion rates based on vitality
            // Older organisms (lower vitality) regenerate stamina more slowly and deplete it faster
            let regen_modifier = 0.5 + (vitality * 0.5); // 0.5 to 1.0 based on vitality
            let depletion_modifier = 1.0 + ((1.0 - vitality) * 0.5); // 1.0 to 1.5 based on vitality

            stamina.regeneration_rate = 5.0 * regen_modifier;
            stamina.depletion_rate = 10.0 * depletion_modifier;
        }
    }
}

/// System to update organism hunger levels
pub fn update_hunger(
    mut commands: Commands,
    mut query: Query<
        (Entity, &mut Hunger, &mut Lifespan, Option<&Dying>),
        With<BiologicalOrganism>,
    >,
    mut organism_died_events: EventWriter<OrganismDied>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (entity, mut hunger, mut lifespan, dying) in query.iter_mut() {
        // Skip entities that are already dying
        if dying.is_some() {
            continue;
        }

        // Increase hunger (decrease food level) over time
        hunger.current_hunger -= hunger.hunger_rate * delta_time.0;
        hunger.current_hunger = hunger.current_hunger.max(0.0);

        // Check if organism is in critical hunger state
        if hunger.is_critical() {
            // Accumulate time in critical state
            hunger.update(delta_time.0);

            // Check if the organism should die from starvation
            if hunger.is_fatal() {
                log::info!("Organism {entity:?} has died from starvation after {:.1} seconds in critical state.",
                          hunger.get_time_in_critical_state());

                // Start the dying process
                commands.entity(entity).insert(Dying::default());
                organism_died_events.write(OrganismDied {
                    entity,
                    cause: DeathCause::Starvation,
                });
                continue;
            }

            // Apply health effects if in critical state for a while
            if hunger.get_time_in_critical_state() > 10.0 {
                // Decrease health based on how long the organism has been starving
                let health_decrease = 0.01 * delta_time.0;
                lifespan.decrease_health(health_decrease);

                if hunger.is_starving() {
                    // Double health decrease if completely starving
                    lifespan.decrease_health(health_decrease);
                }
            }
        }
    }
}

/// System to update organism thirst levels
pub fn update_thirst(
    mut commands: Commands,
    mut query: Query<
        (Entity, &mut Thirst, &mut Lifespan, Option<&Dying>),
        With<BiologicalOrganism>,
    >,
    mut organism_died_events: EventWriter<OrganismDied>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (entity, mut thirst, mut lifespan, dying) in query.iter_mut() {
        // Skip entities that are already dying
        if dying.is_some() {
            continue;
        }

        // Increase thirst (decrease water level) over time
        thirst.current_thirst -= thirst.thirst_rate * delta_time.0;
        thirst.current_thirst = thirst.current_thirst.max(0.0);

        // Check if organism is in critical thirst state
        if thirst.is_critical() {
            // Accumulate time in critical state
            thirst.time_in_critical_state += delta_time.0;

            // Check if the organism should die from dehydration
            if thirst.is_fatal() {
                log::info!("Organism {entity:?} has died from dehydration after {:.1} seconds in critical state.",
                          thirst.time_in_critical_state);

                // Start the dying process
                commands.entity(entity).insert(Dying::default());
                organism_died_events.write(OrganismDied {
                    entity,
                    cause: DeathCause::Dehydration,
                });
                continue;
            }

            // Apply health effects if in critical state for too long
            // Dehydration is more severe than hunger
            if thirst.time_in_critical_state > 5.0 {
                // Decrease health based on how long the organism has been dehydrated
                let health_decrease = 0.02 * delta_time.0;
                lifespan.decrease_health(health_decrease);

                if thirst.is_dehydrated() {
                    // Triple health decrease if completely dehydrated
                    lifespan.decrease_health(health_decrease * 2.0);
                }
            }
        }
    }
}

/// System to regenerate health when an organism is well fed and hydrated
pub fn update_health_regeneration(
    mut query: Query<
        (&Hunger, &Thirst, &mut Lifespan, Option<&Dying>),
        With<BiologicalOrganism>,
    >,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    // Define thresholds for what constitutes "well-fed" and "well hydrated"
    // Using 70% of max as the threshold for both hunger and thirst
    const WELL_FED_THRESHOLD: f32 = 0.7;
    const WELL_HYDRATED_THRESHOLD: f32 = 0.7;
    // Health regeneration rate per second
    const HEALTH_REGEN_RATE: f32 = 0.01;

    for (hunger, thirst, mut lifespan, dying) in query.iter_mut() {
        // Skip entities that are already dying
        if dying.is_some() {
            continue;
        }

        // Check if the organism is well-fed and hydrated
        let is_well_fed = hunger.normalized_hunger() >= WELL_FED_THRESHOLD;
        let is_well_hydrated = thirst.normalized_thirst() >= WELL_HYDRATED_THRESHOLD;

        // If both conditions are met, regenerate health
        if is_well_fed && is_well_hydrated {
            // Calculate health regeneration amount
            // The better fed and hydrated, the faster health regenerates
            let hunger_factor = hunger.normalized_hunger();
            let thirst_factor = thirst.normalized_thirst();
            let regen_multiplier = (hunger_factor + thirst_factor) / 2.0;

            let health_increase = HEALTH_REGEN_RATE * regen_multiplier * delta_time.0;
            lifespan.increase_health(health_increase);
        }
    }
}
