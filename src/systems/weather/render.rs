use crate::components::environment::TerrainPod;
use crate::components::weather::{Cloud, CloudType, WeatherEffect, WeatherEffectType};
use crate::components::GameEntityRender;
use crate::resources::environment::{Climate, TerrainPodGrid};
use avian3d::parry::na::DimAdd;
use bevy::prelude::*;
use bevy_hanabi::prelude::*;
use std::f32::consts::FRAC_PI_2;

/// System to render clouds
pub fn render_clouds(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    query: Query<(Entity, &Cloud), Added<Cloud>>,
    asset_server: Res<AssetServer>,
) {
    for (cloud_entity, cloud) in query.iter() {
        // Create cloud mesh based on type
        let mesh = match cloud.cloud_type {
            CloudType::Cumulus => {
                // Puffy, rounded cloud
                meshes.add(Rectangle::new(cloud.size, cloud.size))
            }
            CloudType::Stratus => {
                // Flat, layered cloud
                meshes.add(Rectangle::new(cloud.size * 1.5, cloud.size * 0.5))
            }
            CloudType::Cirrus => {
                // Wispy, thin cloud
                meshes.add(Rectangle::new(cloud.size * 2.0, cloud.size * 0.3))
            }
            CloudType::Cumulonimbus => {
                // Tall, dense storm cloud
                meshes.add(Rectangle::new(cloud.size * 1.2, cloud.size * 1.5))
            }
            CloudType::Nimbostratus => {
                // Medium-height rain cloud
                meshes.add(Rectangle::new(cloud.size * 1.8, cloud.size * 0.8))
            }
        };

        // Create cloud material based on density
        let alpha = (0.3 + cloud.density * 0.7).min(1.0);
        let darkness = 1.0 - cloud.density * 0.5;

        let color = match cloud.cloud_type {
            CloudType::Cumulonimbus => {
                Color::srgba(darkness * 0.5, darkness * 0.5, darkness * 0.6, alpha)
            } // Dark gray for storm clouds
            _ => Color::srgba(darkness * 0.9, darkness * 0.9, darkness * 0.95, alpha), // White/gray for normal clouds
        };

        let base_path = "images/clouds/";
        let cloud_texture = match cloud.cloud_type {
            CloudType::Cumulonimbus => {
                asset_server.load(base_path.to_string() + "FX_CloudAlpha10.png")
            }
            CloudType::Nimbostratus => {
                asset_server.load(base_path.to_string() + "FX_CloudAlpha05.png")
            }
            CloudType::Stratus => {
                asset_server.load(base_path.to_string() + "FX_CloudAlpha03.png")
            }
            CloudType::Cumulus => {
                asset_server.load(base_path.to_string() + "FX_CloudAlpha02.png")
            }
            CloudType::Cirrus => {
                asset_server.load(base_path.to_string() + "FX_CloudAlpha01.png")
            }
        };

        let material = materials.add(StandardMaterial {
            base_color: color,
            alpha_mode: bevy::prelude::AlphaMode::Add,
            base_color_texture: Some(cloud_texture),
            unlit: true,
            ..default()
        });

        // Add visual components to the cloud entity
        commands
            .spawn((
                Name::new("Cloud Render Visual"),
                Mesh3d(mesh),
                MeshMaterial3d(material),
                GameEntityRender,
            ))
            .insert(ChildOf(cloud_entity));
    }
}

#[derive(Component)]
pub struct RainParticleEffect;

/// System to render weather effects
pub fn render_weather_effects(
    mut commands: Commands,
    mut effects: ResMut<Assets<EffectAsset>>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    query: Query<(Entity, &WeatherEffect, &Transform), Added<WeatherEffect>>,
    climate: Res<Climate>,
) {
    for (entity, effect, transform) in query.iter() {
        match effect.effect_type {
            WeatherEffectType::Rain | WeatherEffectType::Storm => {
                log::info!("Starting rain effect");
                log::info!("Effect type: {:?}", effect.effect_type);
                log::info!("Intensity: {}", effect.intensity);

                // Create rain particle effect
                let is_storm = effect.effect_type == WeatherEffectType::Storm;
                let rain_effect_asset =
                    create_rain_effect(&climate, effect.intensity, is_storm);

                // Spawn the particle effect
                let rain_effect_entity = commands
                    .spawn((
                        ParticleEffect::new(effects.add(rain_effect_asset)),
                        GameEntityRender,
                        RainParticleEffect,
                        Name::new("Weather Particle Rain Effect"),
                    ))
                    .id();

                let splash_effect_asset = create_splash_effect();
                let splash_effect_entity = commands
                    .spawn((
                        ParticleEffect::new(effects.add(splash_effect_asset)),
                        GameEntityRender,
                        RainParticleEffect,
                        Name::new("Weather Particle Splash Effect"),
                        EffectParent::new(rain_effect_entity),
                    ))
                    .id();

                // Need to fix orientation
                // let puddle_effect_asset = create_puddle_effect();
                // let puddle_effect_entity = commands
                //     .spawn((
                //         ParticleEffect::new(effects.add(puddle_effect_asset)),
                //         Transform::default(),
                //         InheritedVisibility::default(),
                //         Name::new("Weather Particle Puddle Effect"),
                //         EffectParent::new(rain_effect_entity),
                //     ))
                //     .id();

                commands
                    .entity(entity)
                    .add_child(rain_effect_entity)
                    // .add_child(puddle_effect_entity)
                    .add_child(splash_effect_entity);

                if let Some(cloud_entity) = effect.cloud {
                    commands.entity(cloud_entity).add_child(entity);
                }

                // TODO: For storms, add lightning flashes (visual only)
                // if is_storm && rng.gen_bool(0.05) {
                //     // Create a brief flash of light
                //     let flash_pos = transform.translation
                //         + Vec3::new(
                //         rng.gen_range(-effect.radius as f32..effect.radius as f32),
                //         rng.gen_range(30.0..50.0),
                //         rng.gen_range(-effect.radius as f32..effect.radius as f32),
                //     );
                //
                //     commands.spawn((
                //         PointLight {
                //             color: Color::srgb(0.9, 0.9, 1.0),
                //             intensity: rng.gen_range(100000.0..500000.0),
                //             range: rng.gen_range(50.0..100.0),
                //             radius: 0.0,
                //             shadows_enabled: true,
                //             ..default()
                //         },
                //         Transform::from_translation(flash_pos),
                //     ));
                // }
            }
            WeatherEffectType::Snow => {
                // Create snow particle effect using bevy_hanabi
                // let effect_handle = create_snow_effect(effect.intensity, &mut effects);
                //
                // // Spawn the particle effect
                // commands.spawn((
                //     ParticleEffect::new(effect_handle),
                //     Transform::from_translation(transform.translation),
                //     Name::new("Snow Effect"),
                // ));
            }
            WeatherEffectType::Fog => {
                // Create fog effect (simple particle system)
                let fog_color = Color::srgba(0.8, 0.8, 0.8, 0.3);

                // Create a fog volume
                let fog_size = effect.radius as f32 * 2.0;
                let fog_mesh = meshes.add(Rectangle::new(fog_size, fog_size));

                let fog_material = materials.add(StandardMaterial {
                    base_color: fog_color,
                    alpha_mode: bevy::prelude::AlphaMode::Blend,
                    unlit: true,
                    ..default()
                });

                commands
                    .spawn((
                        Mesh3d(fog_mesh),
                        MeshMaterial3d(fog_material),
                        GameEntityRender,
                    ))
                    .insert(
                        Transform::from_translation(
                            transform.translation + Vec3::new(0.0, 5.0, 0.0),
                        )
                        .with_rotation(Quat::from_rotation_x(-FRAC_PI_2)),
                    );
            }
            _ => {
                // Other weather effects not implemented yet
            }
        }
    }
}

// Despawn weather effects if it goes out of the pod's bounds
pub fn despawn_weather_effects(
    mut commands: Commands,
    pod_grid: Res<TerrainPodGrid>,
    pod_query: Query<&TerrainPod>,
    particle_effects: Query<(Entity, &GlobalTransform), With<RainParticleEffect>>,
) {
    for (entity, transform) in particle_effects.iter() {
        let position = transform.translation();
        let pod_index = pod_grid.world_to_cell(position);
        if let Some(pod_entity) = pod_grid.get_pod(pod_index.0, pod_index.1) {
            if let Ok(pod) = pod_query.get(*pod_entity) {
                if !pod.is_in_bounds(position) {
                    commands.entity(entity).despawn();
                }
            }
        }
    }
}

/// Create a rain particle effect asset
fn create_rain_effect(
    climate: &Res<Climate>,
    intensity: f32,
    is_storm: bool,
) -> EffectAsset {
    // Create a new expression module
    let writer = ExprWriter::new();

    // Initialize position randomly within a sphere
    let init_pos = SetPositionSphereModifier {
        center: writer.lit(Vec3::ZERO).expr(),
        radius: writer.lit(if is_storm { 2.5 } else { 2.0 }).expr(),
        dimension: ShapeDimension::Volume,
    };

    let age = writer.lit(0.).expr();
    let init_age = SetAttributeModifier::new(Attribute::AGE, age);

    // Define a color gradient for rain
    let mut gradient = Gradient::new();
    if is_storm {
        // Darker blue for storm rain
        gradient.add_key(0.0, Vec4::new(0.5, 0.5, 0.7, 0.7));
        gradient.add_key(1.0, Vec4::new(0.5, 0.5, 0.7, 0.0));
    } else {
        // Light blue for normal rain
        gradient.add_key(0.0, Vec4::new(0.7, 0.7, 0.9, 0.5));
        gradient.add_key(1.0, Vec4::new(0.7, 0.7, 0.9, 0.0));
    }

    // Set initial velocity (downward)
    let fall_speed = if is_storm { 30.0 } else { 20.0 };
    let v = writer
        .lit(Vec3::new(
            -climate.wind_direction.x.min(0.1),
            -(fall_speed * (1. - intensity)),
            0.0,
        ))
        .expr();
    let init_vel = SetAttributeModifier::new(Attribute::VELOCITY, v);

    // Set lifetime
    let lifetime = writer.lit(if is_storm { 0.2 } else { 0.1 }).expr();
    let init_lifetime = SetAttributeModifier::new(Attribute::LIFETIME, lifetime);

    let accel = writer.lit(Vec3::Y * -16.).expr();
    let update_accel = AccelModifier::new(accel);

    let update_spawn_on_die = EmitSpawnEventModifier {
        condition: EventEmitCondition::OnDie,
        count: writer.lit(5u32).expr(),
        child_index: 0,
    };

    let update_spawn_on_die2 = EmitSpawnEventModifier {
        condition: EventEmitCondition::OnDie,
        count: writer.lit(1u32).expr(),
        child_index: 1,
    };

    // Create the effect
    // Spawn rate based on intensity
    let spawn_rate = 100.0 + (intensity * 400.0);
    let effect_id = if is_storm { "StormRain" } else { "Rain" };
    let max_particles = (spawn_rate * 3.0) as u32;

    EffectAsset::new(
        max_particles,
        SpawnerSettings::rate(spawn_rate.into()),
        writer.finish(),
    )
    .with_name(effect_id)
    .init(init_age)
    .init(init_pos)
    .init(init_vel)
    .init(init_lifetime)
    .update(update_accel)
    .update(update_spawn_on_die)
    // .update(update_spawn_on_die2)
    .render(ColorOverLifetimeModifier {
        gradient,
        blend: ColorBlendMode::Overwrite,
        mask: ColorBlendMask::RGBA,
    })
    .render(SizeOverLifetimeModifier {
        gradient: Gradient::constant(Vec3::ONE * 0.02),
        screen_space_size: false,
    })
}

/// Create a splash particle effect asset
fn create_splash_effect() -> EffectAsset {
    let writer = ExprWriter::new();

    let init_pos = InheritAttributeModifier::new(Attribute::POSITION);

    let center = writer.attr(Attribute::POSITION);
    let speed = writer.lit(0.5).uniform(writer.lit(0.5));
    let dir = writer
        .rand(VectorType::VEC3F)
        .mul(writer.lit(0.1))
        .sub(writer.lit(0.1))
        .normalized();

    let init_vel =
        SetAttributeModifier::new(Attribute::VELOCITY, (center + dir * speed).expr());

    let age = writer.lit(0.).expr();
    let init_age = SetAttributeModifier::new(Attribute::AGE, age);

    // Give a bit of variation by randomizing the lifetime per particle
    let lifetime = writer.lit(0.2).uniform(writer.lit(0.5)).expr();
    let init_lifetime = SetAttributeModifier::new(Attribute::LIFETIME, lifetime);

    let accel = writer.lit(Vec3::Y * -16.).expr();
    let update_accel = AccelModifier::new(accel);

    // Add drag to make particles slow down as they ascend
    let drag = writer.lit(10.).expr();
    let update_drag = LinearDragModifier::new(drag);

    // Orient particle toward its velocity to create a cheap 1-particle trail
    let orient = OrientModifier::new(OrientMode::AlongVelocity);

    let spawner = SpawnerSettings::default();

    let mut color_gradient = Gradient::new();
    color_gradient.add_key(0.0, Vec4::new(4.0, 4.0, 4.0, 1.0));
    color_gradient.add_key(0.6, Vec4::new(4.0, 4.0, 4.0, 1.0));
    color_gradient.add_key(1.0, Vec4::new(4.0, 4.0, 4.0, 0.0));

    EffectAsset::new(100, spawner, writer.finish())
        .with_name("Splash")
        .init(init_pos)
        // .init(init_color)
        .init(init_vel)
        .init(init_age)
        .init(init_lifetime)
        .update(update_drag)
        .update(update_accel)
        .render(ColorOverLifetimeModifier {
            gradient: color_gradient,
            blend: ColorBlendMode::Modulate,
            mask: ColorBlendMask::RGBA,
        })
        .render(SizeOverLifetimeModifier {
            gradient: Gradient::constant(Vec3::ONE * 0.005),
            screen_space_size: false,
        })
        .render(orient)
}

fn create_puddle_effect() -> EffectAsset {
    let writer = ExprWriter::new();

    let init_pos = InheritAttributeModifier::new(Attribute::POSITION);

    let spawner = SpawnerSettings::default();

    let mut color_gradient = Gradient::new();
    let color_const = 2.0;
    color_gradient.add_key(0.0, Vec4::new(color_const, color_const, color_const, 0.1));
    color_gradient.add_key(0.6, Vec4::new(color_const, color_const, color_const, 0.5));
    color_gradient.add_key(1.0, Vec4::new(color_const, color_const, color_const, 0.0));

    let age = writer.lit(0.).expr();
    let init_age = SetAttributeModifier::new(Attribute::AGE, age);

    // Give a bit of variation by randomizing the lifetime per particle
    let lifetime = writer.lit(1.2).uniform(writer.lit(1.5)).expr();
    let init_lifetime = SetAttributeModifier::new(Attribute::LIFETIME, lifetime);

    let mut size_gradient = Gradient::new();
    size_gradient.add_key(0.0, Vec3::splat(0.1));
    size_gradient.add_key(0.5, Vec3::splat(0.7));
    size_gradient.add_key(1.0, Vec3::splat(1.));

    let orient =
        OrientModifier::new(OrientMode::default()).with_rotation(writer.lit(1.0).expr());

    let mut module = writer.finish();
    let round = RoundModifier::constant(&mut module, 1.0);

    EffectAsset::new(10, spawner, module)
        .with_name("Splash Puddle")
        .init(init_pos)
        .init(init_age)
        .init(init_lifetime)
        .render(ColorOverLifetimeModifier {
            gradient: color_gradient,
            blend: ColorBlendMode::Modulate,
            mask: ColorBlendMask::RGBA,
        })
        .render(SizeOverLifetimeModifier {
            gradient: size_gradient,
            screen_space_size: false,
        })
        .render(round)
        .render(orient)
}

/// Create a snow particle effect asset
fn create_snow_effect(
    intensity: f32,
    mut effects: &mut ResMut<Assets<EffectAsset>>,
) -> Handle<EffectAsset> {
    // Create a new expression module
    let mut module = Module::default();

    // Define a color gradient for snow
    let mut gradient = Gradient::new();
    gradient.add_key(0.0, Vec4::new(1.0, 1.0, 1.0, 0.8));
    gradient.add_key(1.0, Vec4::new(1.0, 1.0, 1.0, 0.0));

    // Spawn rate based on intensity
    let spawn_rate = 80.0 + (intensity * 300.0);

    // Initialize position randomly within a cylinder
    let init_pos = SetPositionSphereModifier {
        center: module.lit(Vec3::ZERO),
        // height: module.lit(0.1),
        radius: module.lit(8.0),
        dimension: ShapeDimension::Surface,
    };

    // Set initial velocity (slow downward)
    let init_vel = SetVelocitySphereModifier {
        center: module.lit(Vec3::ZERO),
        speed: module.lit(Vec3::new(0.0, -5.0, 0.0)),
    };

    // Add some randomness to velocity for snow (more sideways drift)
    // let velocity = module.attr(Attribute::VELOCITY).add(
    //     module
    //         // .rand(VectorType::VEC3F)
    //         .mul(module.lit(Vec3::new(2.0, 1.0, 2.0)));
    let velocity = module.lit(Vec3::X * 2.);
    let init_vel_random = SetAttributeModifier::new(Attribute::VELOCITY, velocity);

    // Set lifetime (longer for snow)
    let lifetime = module.lit(6.0);
    let init_lifetime = SetAttributeModifier::new(Attribute::LIFETIME, lifetime);

    // Set size (small flakes)
    let init_size = SetSizeModifier {
        size: Vec3::splat(0.1).into(),
    };

    // Create the effect
    let max_particles = (spawn_rate * 6.0) as u32;
    let effect =
        EffectAsset::new(max_particles, SpawnerSettings::rate(spawn_rate.into()), module)
            .with_name("Snow")
            .init(init_pos)
            .init(init_vel)
            .init(init_vel_random)
            .init(init_lifetime)
            .init(init_size)
            .render(ColorOverLifetimeModifier::new(gradient));

    effects.add(effect)
}
