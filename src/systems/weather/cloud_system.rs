use crate::components::environment::{Terrain, TerrainType, WaterType};
use crate::components::lifecycle::CurrentTerrainPod;
use crate::components::weather::{Cloud, CloudType, WeatherEffect, WeatherEffectType};
use crate::resources::environment::{Climate, TerrainPodGrid};
use crate::resources::weather::{AtmosphericConditions, CloudSystem};
use crate::resources::{TimeOfDay, WorldSimulationDeltaTime};
use crate::systems::environment::constants;
use crate::systems::weather::constants::PRECIPITATION_WATER_CONTENT_THRESHOLD;
use bevy::prelude::*;
use rand::{thread_rng, Rng};

/// System to update atmospheric moisture based on evaporation from terrain
pub fn update_atmospheric_moisture(
    mut terrain_query: Query<(&mut Terrain, &CurrentTerrainPod)>,
    mut climate: ResMut<Climate>,
    mut atmospheric_conditions: ResMut<AtmosphericConditions>,
    delta_time: Res<WorldSimulationDeltaTime>,
    time_of_day: Res<TimeOfDay>,
) {
    // Base evaporation rate depends on temperature and delta_time of day
    let base_evaporation_rate = 0.01 * (climate.base_temperature / 30.0).max(0.1);

    // Adjust evaporation based on delta_time of day (more during day, less at night)
    let time_factor = match time_of_day.phase() {
        crate::resources::DayPhase::Morning => 1.2,
        crate::resources::DayPhase::Afternoon => 1.5,
        crate::resources::DayPhase::Evening => 0.8,
        crate::resources::DayPhase::Night => 0.4,
    };

    let adjusted_evaporation_rate = base_evaporation_rate * time_factor;

    // Track total evaporation for this update
    let mut total_evaporation = 0.0;

    // Calculate evaporation from each terrain cell
    for (mut terrain, current_pod) in terrain_query.iter_mut() {
        let Some(mut pod_conditions) = atmospheric_conditions
            .get_pod_conditions_mut(current_pod.pod_index.0, current_pod.pod_index.1)
        else {
            continue;
        };

        // Calculate evaporation based on moisture, temperature, and terrain type
        let mut evaporation_amount = 0.0;

        // Water bodies evaporate more than land
        match terrain.terrain_type {
            TerrainType::Water(water_type) => {
                // Different water bodies have different evaporation rates
                let water_factor = match water_type {
                    WaterType::Ocean => 1.0,
                    WaterType::Lake => 0.9,
                    WaterType::River => 0.8,
                    WaterType::Swamp => 0.7,
                };

                evaporation_amount =
                    adjusted_evaporation_rate * water_factor * delta_time.0;
            }
            _ => {
                // Land evaporation depends on moisture content
                evaporation_amount = adjusted_evaporation_rate
                    * terrain.properties.moisture
                    * 0.1
                    * delta_time.0;
            }
        }

        // Apply evaporation to terrain moisture
        if terrain.properties.moisture > 0.0 {
            terrain.properties.moisture -= evaporation_amount;
            terrain.properties.moisture = terrain.properties.moisture.max(0.0);

            // Add to total evaporation
            total_evaporation += evaporation_amount;
        }

        pod_conditions.moisture += evaporation_amount * 0.1;
        pod_conditions.moisture = pod_conditions.moisture.min(1.0);

        pod_conditions.dew_point =
            climate.base_temperature - ((1.0 - pod_conditions.moisture) * 100.0) / 5.0;
    }

    // Update climate humidity based on atmospheric moisture
    // Tuning: above 50°C, no humidity; adjust to your game’s scale
    let max_temp = constants::MAX_TEMPERATURE_FOR_HUMIDITY;
    let base_temp = climate.base_temperature;

    // in °C
    // Simple linear “dry‐out” factor
    let temp_factor = ((max_temp - base_temp) / max_temp).clamp(0.0, 1.0);

    // Convert moisture → humidity
    climate.humidity = (atmospheric_conditions.moisture * temp_factor).clamp(0.0, 1.0);
}

/// System to form clouds based on atmospheric moisture
pub fn form_clouds(
    mut commands: Commands,
    mut atmospheric_conditions: ResMut<AtmosphericConditions>,
    mut cloud_system: ResMut<CloudSystem>,
    pod_grid: Option<Res<TerrainPodGrid>>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    // Update delta_time since last formation
    cloud_system.time_since_last_formation += delta_time.0;

    // Initialize pod conditions if we have a pod grid
    if let Some(pod_grid) = pod_grid.as_ref() {
        atmospheric_conditions.initialize_pod_conditions(pod_grid);
    }

    // Check if we should form new clouds
    if atmospheric_conditions.moisture > cloud_system.formation_threshold
        && cloud_system.time_since_last_formation > (1.0 / cloud_system.formation_rate)
    {
        // Reset timer
        cloud_system.time_since_last_formation = 0.0;

        // Process each pod if we have a pod grid
        if let Some(pod_grid) = pod_grid.as_ref() {
            for (&pod_index, pod_entity) in pod_grid.pods.iter() {
                let cloud_entities = form_clouds_for_pod(
                    pod_index,
                    &mut commands,
                    &mut atmospheric_conditions,
                    &mut cloud_system,
                );
                commands.entity(*pod_entity).add_children(&*cloud_entities);
            }
        }
    }
}

/// Form clouds for a specific pod
fn form_clouds_for_pod(
    pod_index: (i32, i32),
    commands: &mut Commands,
    atmospheric_conditions: &mut AtmosphericConditions,
    cloud_system: &mut CloudSystem,
) -> Vec<Entity> {
    let atmosphere_stability = atmospheric_conditions.stability;
    let mut entities = vec![];

    // Get pod conditions
    if let Some(pod_conditions) =
        atmospheric_conditions.get_pod_conditions_mut(pod_index.0, pod_index.1)
    {
        // Determine number of clouds to form based on moisture level
        let cloud_count = (pod_conditions.moisture * 3.0).ceil() as usize;

        // Form clouds in regions with high moisture
        let mut rng = thread_rng();
        let mut clouds_formed = 0;

        // Generate random regions for cloud formation
        let mut regions = Vec::new();

        // Create some random regions for cloud formation
        for _ in 0..cloud_count.min(5) {
            let region = (rng.gen_range(0..10), rng.gen_range(0..10));
            regions.push(region);
        }

        // Form clouds in random regions
        for &region in regions.iter().take(cloud_count) {
            if pod_conditions.moisture > cloud_system.formation_threshold {
                // Determine cloud type based on atmospheric stability
                let cloud_type = if atmosphere_stability < 0.3 {
                    CloudType::Cumulonimbus // Unstable air = storm clouds
                } else if atmosphere_stability < 0.6 {
                    CloudType::Nimbostratus // Moderately stable = rain clouds
                } else {
                    // Choose between fair weather clouds
                    match rng.gen_range(0..3) {
                        0 => CloudType::Cumulus,
                        1 => CloudType::Stratus,
                        _ => CloudType::Cirrus,
                    }
                };

                // Determine cloud properties
                let density = pod_conditions.moisture * rng.gen_range(0.7..1.0);
                let size = rng.gen_range(0.1..2.0);

                let mut altitude = match cloud_type {
                    CloudType::Cirrus => rng.gen_range(80.0..100.0),
                    CloudType::Cumulonimbus => rng.gen_range(30.0..60.0),
                    CloudType::Nimbostratus => rng.gen_range(20.0..40.0),
                    _ => rng.gen_range(40.0..70.0),
                };

                // Add some randomness to position
                let pos_x = rng.gen_range(-2.0..=-1.0);
                let pos_y = altitude.remap(0.0, 100.0, 3.0, 5.0);
                let pos_z = rng.gen_range(0.0..3.0);

                // Clouds should be flat
                // let rotation = Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2);
                let rotation = Quat::from_rotation_x(-0.5);

                // Spawn cloud entity
                let cloud_entity = commands
                    .spawn((
                        Cloud {
                            density,
                            size,
                            altitude,
                            cloud_type,
                            water_content: density * 50.0, // Initial water content
                            ..default()
                        },
                        InheritedVisibility::default(),
                        Transform::from_translation(Vec3::new(pos_x, pos_y, pos_z))
                            .with_rotation(rotation),
                        Name::new(format!("Cloud ({:?})", cloud_type)),
                    ))
                    .id();

                // Add to cloud system
                cloud_system.clouds.push(cloud_entity);
                entities.push(cloud_entity);

                // Update pod conditions
                pod_conditions.moisture -= 0.01;
                pod_conditions.cloud_cover += 0.02;
                pod_conditions.cloud_cover = pod_conditions.cloud_cover.min(1.0);

                clouds_formed += 1;

                // Limit the number of clouds formed per update
                if clouds_formed >= 3 {
                    break;
                }
            }
        }

        // Update global averages
        atmospheric_conditions.update_global_averages();
    }

    entities
}

pub fn update_climate_rainfall(
    mut climate: ResMut<Climate>,
    clouds_query: Query<&Children, With<Cloud>>,
    weather_effects: Query<&WeatherEffect>,
) {
    // Calculate total rainfall from clouds
    let mut total_rainfall = 0.0;
    for children in clouds_query.iter() {
        for child in children.iter() {
            if let Ok(effect) = weather_effects.get(child) {
                if effect.effect_type == WeatherEffectType::Rain
                    || effect.effect_type == WeatherEffectType::Storm
                {
                    total_rainfall += effect.intensity;
                }
            }
        }
    }

    // Update climate rainfall
    climate.rainfall = total_rainfall;
    climate.rainfall = climate.rainfall.min(1.0);
}

/// System to update existing clouds
pub fn update_clouds(
    mut commands: Commands,
    mut cloud_query: Query<(Entity, &mut Cloud)>,
    mut cloud_system: ResMut<CloudSystem>,
    mut atmospheric_conditions: ResMut<AtmosphericConditions>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    let mut clouds_to_remove = Vec::new();

    for (entity, mut cloud) in cloud_query.iter_mut() {
        // Update cloud water content based on atmospheric conditions
        if atmospheric_conditions.moisture > 0.1
            && cloud.water_content < cloud.max_water_content
        {
            // Clouds accumulate more water in humid conditions
            cloud.water_content += atmospheric_conditions.moisture * 0.5 * delta_time.0;
            cloud.density = (cloud.water_content / cloud.max_water_content).min(1.0);
        }

        // Check if cloud should become precipitation-ready
        if cloud.water_content
            > cloud.max_water_content * PRECIPITATION_WATER_CONTENT_THRESHOLD
        {
            cloud.precipitation_ready = true;
        }

        // Natural dissipation
        let dissipation_rate = match cloud.cloud_type {
            CloudType::Cirrus => cloud_system.dissipation_rate * 1.5, // Cirrus clouds dissipate faster
            CloudType::Cumulonimbus => cloud_system.dissipation_rate * 0.5, // Storm clouds are more persistent
            _ => cloud_system.dissipation_rate,
        };

        cloud.water_content -= dissipation_rate * delta_time.0;

        // Remove clouds that have dissipated
        if cloud.water_content <= 0.0 {
            clouds_to_remove.push(entity);

            // Return some moisture to the atmosphere
            atmospheric_conditions.moisture += 0.005;
            atmospheric_conditions.cloud_cover -= 0.02;
            atmospheric_conditions.cloud_cover =
                atmospheric_conditions.cloud_cover.max(0.0);

            // No longer updating local conditions
            continue;
        }

        // Update cloud density based on water content
        cloud.density = (cloud.water_content / cloud.max_water_content).min(1.0);
    }

    // Remove dissipated clouds
    for entity in clouds_to_remove {
        cloud_system.clouds.retain(|&e| e != entity);
        commands.entity(entity).despawn();
    }
}

/// System to move clouds based on wind
pub fn cloud_movement(
    mut cloud_query: Query<(&Cloud, &mut Transform)>,
    delta_time: Res<WorldSimulationDeltaTime>,
    climate: Res<Climate>,
) {
    for (cloud, mut transform) in cloud_query.iter_mut() {
        // Calculate movement based on wind direction and speed
        let wind_direction = climate.wind_direction.normalize_or_zero();
        let wind_speed = climate.wind_speed;

        // Adjust speed based on cloud type and altitude
        let altitude_factor = (cloud.altitude / 100.0).min(1.0);
        let cloud_type_factor = match cloud.cloud_type {
            CloudType::Cirrus => 1.5, // High altitude clouds move faster
            CloudType::Cumulonimbus => 0.7, // Storm clouds move slower
            _ => 1.0,
        };

        let adjusted_speed = wind_speed * cloud_type_factor * altitude_factor;

        // Move the cloud
        let movement = Vec3::new(
            wind_direction.x * adjusted_speed * delta_time.0,
            0.0, // Clouds maintain altitude
            wind_direction.y * adjusted_speed * delta_time.0,
        );

        transform.translation += movement;
    }
}
