use crate::components::environment::{Terrain, TerrainEvent, TerrainEventType};
use crate::components::weather::{Cloud, CloudType, WeatherEffect, WeatherEffectType};
use crate::resources::environment::{
    Climate, EnvironmentEvents, GlobalEnvironmentEvent, GlobalEventType, TerrainPodGrid
};
use crate::resources::weather::AtmosphericConditions;
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;
use rand::{thread_rng, Rng};
use std::collections::HashMap;

/// System to trigger precipitation from clouds
pub fn trigger_precipitation(
    mut commands: Commands,
    mut cloud_query: Query<(Entity, &mut Cloud, &Transform)>,
    mut atmospheric_conditions: ResMut<AtmosphericConditions>,
    mut environment_events: ResMut<EnvironmentEvents>,
    delta_time: Res<WorldSimulationDeltaTime>,
    climate: Res<Climate>,
) {
    let mut rng = thread_rng();

    // Track precipitation by region
    let precipitation_regions: HashMap<(i32, i32), (f32, WeatherEffectType)> =
        HashMap::new();

    for (cloud_entity, mut cloud, transform) in cloud_query.iter_mut() {
        // Skip if there's an active weather effect
        if let Some(active_effect) = atmospheric_conditions.precipitation_type {
            if active_effect == WeatherEffectType::Rain
                || active_effect == WeatherEffectType::Snow
            {
                continue;
            }
        }

        // Check if cloud is ready for precipitation (at least x% full)
        if cloud.precipitation_ready {
            // Determine precipitation type based on temperature
            let precipitation_type = if climate.base_temperature < 0.0 {
                WeatherEffectType::Snow
            } else if climate.base_temperature < 2.0
                && cloud.cloud_type == CloudType::Cumulonimbus
            {
                WeatherEffectType::Hail
            } else if cloud.cloud_type == CloudType::Cumulonimbus && rng.gen_bool(0.7) {
                WeatherEffectType::Storm
            } else {
                WeatherEffectType::Rain
            };

            // Calculate precipitation intensity based on cloud density and type
            let base_intensity = cloud.density * 0.8;
            let type_factor = match cloud.cloud_type {
                CloudType::Cumulonimbus => 1.5, // Thunderstorms are intense
                CloudType::Nimbostratus => 1.2, // Rain clouds produce steady rain
                _ => 0.8,                       // Other clouds produce light rain
            };

            let precipitation_intensity = (base_intensity * type_factor).min(1.0);

            // Calculate precipitation amount (how much water leaves the cloud)
            let precipitation_amount =
                cloud.water_content * 0.2 * precipitation_intensity * delta_time.0;

            // Reduce cloud water content
            cloud.water_content -= precipitation_amount;

            // If water content drops below threshold, cloud is no longer precipitation-ready
            if cloud.water_content < cloud.max_water_content * 0.5 {
                cloud.precipitation_ready = false;
            }

            // Create weather effect entity for visualization
            let effect_radius = match precipitation_type {
                WeatherEffectType::Storm => 10,
                WeatherEffectType::Rain => 5,
                _ => 8,
            };

            log::info!(
                "Spawned weather effect: {:?} at position {:?}",
                precipitation_type,
                transform.translation
            );

            let duration = match precipitation_type {
                WeatherEffectType::Storm => rng.gen_range(300.0..600.0),
                WeatherEffectType::Rain => rng.gen_range(180.0..300.0),
                _ => rng.gen_range(120.0..240.0),
            };

            commands.spawn((
                WeatherEffect::new(
                    precipitation_type,
                    precipitation_intensity,
                    duration,
                    effect_radius,
                    Some(cloud_entity),
                ),
                Transform::default(),
                InheritedVisibility::default(),
                Name::new(format!("{:?} Effect", precipitation_type)),
            ));

            // Update atmospheric conditions
            atmospheric_conditions.precipitation_intensity = atmospheric_conditions
                .precipitation_intensity
                .max(precipitation_intensity);
            atmospheric_conditions.precipitation_type = Some(precipitation_type);

            // For storms, create a global event
            if precipitation_type == WeatherEffectType::Storm
                && precipitation_intensity > 0.7
            {
                let storm_event = GlobalEnvironmentEvent {
                    event_type: GlobalEventType::Storm,
                    duration: rng.gen_range(300.0..600.0), // 5-10 minutes
                    intensity: precipitation_intensity,
                    time_remaining: rng.gen_range(300.0..600.0),
                };

                environment_events.active_events.push(storm_event);
            }
        }
    }

    // Apply precipitation to terrain in affected regions
    for (region, (intensity, effect_type)) in precipitation_regions {
        // Create terrain events for each region
        let event_type = match effect_type {
            WeatherEffectType::Rain | WeatherEffectType::Storm => {
                TerrainEventType::Rainfall
            }
            // WeatherEffectType::Snow => TerrainEventType::Snowfall,
            _ => TerrainEventType::Rainfall,
        };

        // Add local event to environment events
        let local_event = crate::resources::environment::LocalEnvironmentEvent {
            event_type: match effect_type {
                WeatherEffectType::Storm => {
                    crate::resources::environment::LocalEventType::Flood
                }
                _ => crate::resources::environment::LocalEventType::Flood, // Default to flood for now
            },
            duration: 300.0, // 5 minutes
            intensity,
            time_remaining: 300.0,
            radius: 10,
        };

        environment_events
            .local_events
            .entry(region)
            .or_insert_with(Vec::new)
            .push(local_event);
    }
}

/// System to handle precipitation effects on terrain
pub fn handle_precipitation(
    mut commands: Commands,
    mut terrain_query: Query<(Entity, &mut Terrain, &Transform)>,
    clouds_query: Query<(Entity, &GlobalTransform), With<Cloud>>,
    weather_effects: Query<&WeatherEffect>,
    delta_time: Res<WorldSimulationDeltaTime>,
    terrain_pod_grid: Res<TerrainPodGrid>,
) {
    // Process each weather effect
    for (cloud_entity, cloud_transform) in clouds_query.iter() {
        let Ok(effect) = weather_effects.get(cloud_entity) else {
            continue;
        };

        let effect_pos = cloud_transform.translation();
        let effect_radius_squared =
            (effect.radius as f32 * terrain_pod_grid.cell_size).powi(2);

        // Apply effects to terrain within radius
        for (entity, mut terrain, transform) in terrain_query.iter_mut() {
            let terrain_pos = transform.translation;
            let distance_squared = (terrain_pos.x - effect_pos.x).powi(2)
                + (terrain_pos.z - effect_pos.z).powi(2);

            // Check if terrain is within effect radius
            if distance_squared <= effect_radius_squared {
                // Calculate intensity falloff with distance
                let distance_factor =
                    1.0 - (distance_squared / effect_radius_squared).sqrt();
                let local_intensity = effect.intensity * distance_factor;

                match effect.effect_type {
                    WeatherEffectType::Rain | WeatherEffectType::Storm => {
                        // Increase moisture based on rain intensity
                        let moisture_increase = local_intensity * 0.05 * delta_time.0;
                        terrain.properties.moisture += moisture_increase;
                        terrain.properties.moisture = terrain
                            .properties
                            .moisture
                            .min(terrain.properties.water_retention);

                        // For heavy rain or storms, add terrain event
                        if local_intensity > 0.6 {
                            commands.entity(entity).insert(TerrainEvent {
                                event_type: TerrainEventType::Rainfall,
                                duration: 300.0, // 5 minutes
                                intensity: local_intensity,
                                time_remaining: 300.0,
                            });
                        }
                    }
                    WeatherEffectType::Snow => {
                        // Snow adds moisture more slowly but lasts longer
                        let moisture_increase = local_intensity * 0.02 * delta_time.0;
                        terrain.properties.moisture += moisture_increase;
                        terrain.properties.moisture = terrain
                            .properties
                            .moisture
                            .min(terrain.properties.water_retention);

                        // Snow also cools the terrain
                        terrain.properties.temperature -=
                            local_intensity * 0.5 * delta_time.0;
                    }
                    WeatherEffectType::Hail => {
                        // Hail adds less moisture but can damage plants (handled elsewhere)
                        let moisture_increase = local_intensity * 0.01 * delta_time.0;
                        terrain.properties.moisture += moisture_increase;
                        terrain.properties.moisture = terrain
                            .properties
                            .moisture
                            .min(terrain.properties.water_retention);
                    }
                    WeatherEffectType::Fog => {
                        // Fog slightly increases moisture
                        let moisture_increase = local_intensity * 0.01 * delta_time.0;
                        terrain.properties.moisture += moisture_increase;
                        terrain.properties.moisture = terrain
                            .properties
                            .moisture
                            .min(terrain.properties.water_retention);
                    }
                }
            }
        }
    }
}

/// System to update weather effects
pub fn update_weather_effects(
    mut commands: Commands,
    mut weather_effects: Query<(Entity, &mut WeatherEffect)>,
    delta_time: Res<WorldSimulationDeltaTime>,
    mut atmospheric_conditions: ResMut<AtmosphericConditions>,
) {
    let mut active_effects = false;
    let mut max_intensity = 0.0;
    let mut dominant_effect = None;

    for (entity, mut effect) in weather_effects.iter_mut() {
        // Update remaining delta_time
        effect.time_remaining -= delta_time.0;

        // Remove expired effects
        if effect.time_remaining <= 0.0 {
            commands.entity(entity).despawn();
            continue;
        }

        active_effects = true;

        // Track the most intense effect
        if effect.intensity > max_intensity {
            max_intensity = effect.intensity;
            dominant_effect = Some(effect.effect_type);
        }
    }

    // Update global atmospheric conditions
    if active_effects {
        atmospheric_conditions.precipitation_intensity = max_intensity;
        atmospheric_conditions.precipitation_type = dominant_effect;
    } else {
        atmospheric_conditions.precipitation_intensity = 0.0;
        atmospheric_conditions.precipitation_type = None;
    }

    // No longer updating local conditions
}
