pub mod debug_visualizations;

use crate::components::lifecycle::Organism;
use crate::components::perception::*;
use crate::resources::TimeOfDay;
use bevy::prelude::*;

/// System to process stimulus broadcasts and detect them with appropriate senses
pub fn process_stimulus_broadcasts(
    time_of_day: Res<TimeOfDay>,
    mut broadcast_events: EventReader<BroadcastStimulusEvent>,
    mut detection_events: EventWriter<StimulusDetectedEvent>,
    perception_query: Query<(
        Entity,
        &GlobalTransform,
        &AIPerception,
        Option<&SightPerception>,
        Option<&HearingPerception>,
        Option<&TouchPerception>,
        Option<&DamagePerception>,
        Option<&SmellPerception>,
    )>,
    source_query: Query<&GlobalTransform>,
) {
    let elapsed = time_of_day.elapsed_seconds;
    for event in broadcast_events.read() {
        // Get the source transform
        if let Ok(_source_transform) = source_query.get(event.source_entity) {
            // Check all entities with perception components
            for (
                entity,
                global_transform,
                perception,
                sight,
                hearing,
                touch,
                damage,
                smell,
            ) in perception_query.iter()
            {
                // Skip if the entity is the source
                if entity == event.source_entity {
                    continue;
                }

                // Calculate distance between source and perceiver
                let distance = global_transform.translation().distance(event.location);

                // Check if the stimulus is within range
                if distance > event.range {
                    continue;
                }

                // Calculate base detection strength based on distance
                let distance_factor = 1.0 - (distance / event.range).clamp(0.0, 1.0);
                let base_strength = event.strength * distance_factor;

                // Process based on stimulus type and available senses
                match event.stimulus_type {
                    StimulusType::Visual => {
                        if let Some(sight) = sight {
                            if !sight.active
                                || !perception.active_senses.contains(&SenseType::Sight)
                            {
                                continue;
                            }

                            // Check if within sight range
                            if distance > sight.sight_radius {
                                continue;
                            }

                            // Check if within field of view
                            let direction = (event.location
                                - global_transform.translation())
                            .normalize();
                            let forward = global_transform.forward();
                            let angle = forward.angle_between(direction).to_degrees();

                            if angle > sight.fov * 0.5 {
                                // Outside main FOV, check peripheral vision
                                if angle > sight.fov && sight.peripheral_vision <= 0.0 {
                                    continue;
                                }

                                // Reduce strength based on peripheral vision
                                let peripheral_factor = 1.0
                                    - (angle - sight.fov * 0.5)
                                        / (180.0 - sight.fov * 0.5);

                                let strength = base_strength
                                    * peripheral_factor
                                    * sight.peripheral_vision;

                                if strength <= 0.05 {
                                    // Minimum threshold for detection
                                    continue;
                                }

                                send_detection_event(
                                    entity,
                                    event,
                                    strength,
                                    elapsed,
                                    &mut detection_events,
                                );
                            } else {
                                // Within main FOV
                                send_detection_event(
                                    entity,
                                    event,
                                    base_strength,
                                    elapsed,
                                    &mut detection_events,
                                );
                            }
                        }
                    }
                    StimulusType::Sound => {
                        if let Some(hearing) = hearing {
                            if !hearing.active
                                || !perception.active_senses.contains(&SenseType::Hearing)
                            {
                                continue;
                            }

                            // Check if within hearing range
                            if distance > hearing.hearing_range {
                                continue;
                            }

                            let strength = base_strength * hearing.sensitivity;
                            if strength <= 0.05 {
                                // Minimum threshold for detection
                                continue;
                            }

                            send_detection_event(
                                entity,
                                event,
                                strength,
                                elapsed,
                                &mut detection_events,
                            );
                        }
                    }
                    StimulusType::Touch => {
                        if let Some(touch) = touch {
                            if !touch.active
                                || !perception.active_senses.contains(&SenseType::Touch)
                            {
                                continue;
                            }

                            // Touch requires very close proximity
                            if distance > 0.5 {
                                continue;
                            }

                            let strength = base_strength * touch.sensitivity;
                            send_detection_event(
                                entity,
                                event,
                                strength,
                                elapsed,
                                &mut detection_events,
                            );
                        }
                    }
                    StimulusType::Damage => {
                        if let Some(damage) = damage {
                            if !damage.active
                                || !perception.active_senses.contains(&SenseType::Damage)
                            {
                                continue;
                            }

                            // Damage perception doesn't depend on distance as much
                            let strength = event.strength * damage.sensitivity;
                            send_detection_event(
                                entity,
                                event,
                                strength,
                                elapsed,
                                &mut detection_events,
                            );
                        }
                    }
                    StimulusType::Smell => {
                        if let Some(smell) = smell {
                            if !smell.active
                                || !perception.active_senses.contains(&SenseType::Smell)
                            {
                                continue;
                            }

                            // log::info!("Checking smell: distance: {distance}, smell range: {}", smell.smell_range);

                            // Check if within smell range
                            if distance > smell.smell_range {
                                continue;
                            }

                            let strength = base_strength * smell.sensitivity;

                            if strength <= 0.05 {
                                // Minimum threshold for detection
                                continue;
                            }

                            send_detection_event(
                                entity,
                                event,
                                strength,
                                elapsed,
                                &mut detection_events,
                            );
                        }
                    }
                    StimulusType::Movement => {
                        if let Some(sight) = sight {
                            if !sight.active
                                || !perception.active_senses.contains(&SenseType::Sight)
                            {
                                continue;
                            }

                            // Movement detection uses sight but is more sensitive to motion
                            if distance > sight.sight_radius * 1.2 {
                                // Slightly increased range for movement
                                continue;
                            }

                            // Check if within field of view (wider for movement detection)
                            let direction = (event.location
                                - global_transform.translation())
                            .normalize();
                            let forward = global_transform.forward();
                            let angle = forward.angle_between(direction).to_degrees();
                            let movement_fov = sight.fov * 1.2; // Wider FOV for movement

                            if angle > movement_fov * 0.5 {
                                // Outside main FOV, check peripheral vision
                                if angle > movement_fov && sight.peripheral_vision <= 0.0
                                {
                                    continue;
                                }

                                // Reduce strength based on peripheral vision
                                let peripheral_factor = 1.0
                                    - (angle - movement_fov * 0.5)
                                        / (180.0 - movement_fov * 0.5);
                                let strength = base_strength
                                    * peripheral_factor
                                    * sight.peripheral_vision
                                    * 1.5; // Movement is easier to detect

                                if strength <= 0.05 {
                                    // Minimum threshold for detection
                                    continue;
                                }

                                send_detection_event(
                                    entity,
                                    event,
                                    strength,
                                    elapsed,
                                    &mut detection_events,
                                );
                            } else {
                                // Within main FOV
                                send_detection_event(
                                    entity,
                                    event,
                                    base_strength * 1.5,
                                    elapsed,
                                    &mut detection_events,
                                );
                            }
                        }
                    }
                }
            }
        }
    }
}

/// Helper function to send a detection event
fn send_detection_event(
    detector_entity: Entity,
    broadcast_event: &BroadcastStimulusEvent,
    strength: f32,
    current_time: f32,
    detection_events: &mut EventWriter<StimulusDetectedEvent>,
) {
    let stimulus = DetectedStimulus {
        stimulus_type: broadcast_event.stimulus_type,
        source_entity: Some(broadcast_event.source_entity),
        location: broadcast_event.location,
        strength,
        detection_time: current_time,
        data: broadcast_event.data.clone(),
    };

    detection_events.write(StimulusDetectedEvent {
        detector_entity,
        stimulus,
    });
}

/// System to update perception memory and forget old stimuli
pub fn update_perception_memory(
    time_of_day: Res<TimeOfDay>,
    mut perception_query: Query<(Entity, &mut AIPerception)>,
    mut forgotten_events: EventWriter<StimulusForgottenEvent>,
) {
    let current_time = time_of_day.elapsed_seconds;

    for (entity, mut perception) in perception_query.iter_mut() {
        // Process short-term memory
        let short_term_duration = perception.short_term_memory_duration;
        if short_term_duration > 0.0 {
            let memory_importance_threshold = perception.memory_importance_threshold;

            let mut items_to_keep_long_term = Vec::new();

            let detected_stimuli = perception.detected_stimuli.clone();

            // Check for stimuli that should be forgotten or moved to long-term memory
            perception.detected_stimuli.retain(|stimulus| {
                let age = (current_time - stimulus.detection_time).abs();
                let should_keep = age < short_term_duration;

                if !should_keep {
                    // Before forgetting, check if this stimulus should be stored in long-term memory
                    // Also check if it's been encountered multiple times
                    let encounter_count = detected_stimuli
                        .iter()
                        .filter(|s| {
                            s.stimulus_type == stimulus.stimulus_type
                                && s.source_entity == stimulus.source_entity
                        })
                        .count();

                    if stimulus.strength >= memory_importance_threshold
                        || encounter_count > 50
                    {
                        // Determine memory category based on stimulus type
                        let category = match stimulus.stimulus_type {
                            StimulusType::Visual => {
                                if let StimulusData::Visual { size, .. } = &stimulus.data
                                {
                                    if size > &0.5 {
                                        MemoryCategory::Entity
                                    } else {
                                        MemoryCategory::General
                                    }
                                } else {
                                    MemoryCategory::General
                                }
                            }
                            StimulusType::Sound => MemoryCategory::General,
                            StimulusType::Touch => MemoryCategory::Entity,
                            StimulusType::Damage => MemoryCategory::Threat,
                            StimulusType::Smell => {
                                // Check if this is a food stimulus
                                if let StimulusData::Food { is_consumable, .. } =
                                    &stimulus.data
                                {
                                    if *is_consumable {
                                        MemoryCategory::Resource
                                    } else {
                                        MemoryCategory::General
                                    }
                                } else {
                                    // Smells with high strength might be resources
                                    if stimulus.strength > 0.8 {
                                        MemoryCategory::Resource
                                    } else {
                                        MemoryCategory::General
                                    }
                                }
                            }
                            StimulusType::Movement => MemoryCategory::Entity,
                        };

                        items_to_keep_long_term.push((stimulus.clone(), category));
                    }

                    // Send a forgotten event
                    forgotten_events.write(StimulusForgottenEvent {
                        detector_entity: entity,
                        stimulus_type: stimulus.stimulus_type,
                        source_entity: stimulus.source_entity,
                    });
                }

                should_keep
            });

            for (stimulus, category) in items_to_keep_long_term.iter() {
                // Add to long-term memory
                perception.add_to_long_term_memory(
                    stimulus,
                    stimulus.strength,
                    category.clone(),
                    current_time,
                    None,
                );
            }
        }

        // Process long-term memory
        let long_term_duration = perception.long_term_memory_duration;
        if long_term_duration > 0.0 {
            // Remove memories that are too old
            perception.long_term_memory.retain(|memory| {
                let age = (current_time - memory.last_encounter_time).abs();

                // Keep memories that are still within duration or have high importance and encounter count
                let should_keep = age < long_term_duration
                    || (memory.importance > 0.9 && memory.encounter_count > 6);

                if !should_keep {
                    // Send a forgotten event for long-term memory items too
                    forgotten_events.write(StimulusForgottenEvent {
                        detector_entity: entity,
                        stimulus_type: memory.stimulus.stimulus_type,
                        source_entity: memory.stimulus.source_entity,
                    });
                }

                should_keep
            });
        }
    }
}

/// System to handle stimulus detection events
pub fn handle_stimulus_detection(
    time_of_day: Res<TimeOfDay>,
    mut detection_events: EventReader<StimulusDetectedEvent>,
    mut perception_query: Query<&mut AIPerception>,
) {
    let current_time = time_of_day.elapsed_seconds;

    for event in detection_events.read() {
        if let Ok(mut perception) = perception_query.get_mut(event.detector_entity) {
            // Add to detected stimuli (short-term memory)
            perception.detected_stimuli.push(event.stimulus.clone());

            // Check if this stimulus matches any existing long-term memory
            if let Some(source_entity) = event.stimulus.source_entity {
                // Check if we already have a memory of this entity with the same stimulus type
                let memory_index =
                    perception.long_term_memory.iter().position(|memory| {
                        memory.stimulus.source_entity.is_some()
                            && memory.stimulus.source_entity.unwrap() == source_entity
                            && memory.stimulus.stimulus_type
                                == event.stimulus.stimulus_type
                    });

                if let Some(index) = memory_index {
                    // Update existing memory
                    let memory = &mut perception.long_term_memory[index];
                    memory.last_encounter_time = current_time;
                    memory.encounter_count += 1;

                    // Increase importance if this stimulus is stronger
                    if event.stimulus.strength > memory.importance {
                        memory.importance = event.stimulus.strength;
                    }

                    // Update the stimulus data with the most recent one
                    memory.stimulus = event.stimulus.clone();
                }
                // Note: We don't automatically create new long-term memories here
                // That happens in update_perception_memory when stimuli expire from short-term memory
            }
        }
    }
}

/// Helper function to determine range based on stimulus type
fn get_stimulus_range(stimulus_type: StimulusType) -> f32 {
    match stimulus_type {
        StimulusType::Visual => 30.0,
        StimulusType::Sound => 20.0,
        StimulusType::Touch => 0.5,
        StimulusType::Damage => 10.0,
        StimulusType::Smell => 15.0,
        StimulusType::Movement => 25.0,
    }
}

/// System to automatically broadcast stimuli from stimulus sources
pub fn broadcast_from_sources(
    sources_query: Query<(Entity, &GlobalTransform, &StimulusSources)>,
    mut broadcast_events: EventWriter<BroadcastStimulusEvent>,
) {
    // Process entities with stimulus sources
    for (entity, transform, sources) in sources_query.iter() {
        for source in sources.iter() {
            if !source.active {
                continue;
            }

            let range = get_stimulus_range(source.stimulus_type);

            broadcast_events.write(BroadcastStimulusEvent {
                source_entity: entity,
                stimulus_type: source.stimulus_type,
                location: transform.translation(),
                strength: source.strength,
                range,
                data: source.data.clone(),
            });
        }
    }
}

/// System to add perception components to organisms that need them
pub fn setup_organism_perception(
    mut commands: Commands,
    query: Query<Entity, (Added<Organism>, Without<AIPerception>)>,
) {
    for entity in query.iter() {
        // Add basic perception components
        commands.entity(entity).insert(AIPerception {
            active_senses: vec![
                SenseType::Sight,
                SenseType::Hearing,
                SenseType::Touch,
                SenseType::Damage,
                SenseType::Smell,
                SenseType::Prediction,
            ],
            short_term_memory_duration: 5.0, // 5 seconds short-term memory
            long_term_memory_duration: 300.0, // 5 minutes long-term memory
            memory_importance_threshold: 0.7, // Only remember important things
            ..default()
        });
    }
}
