use crate::components::environment::grass::GrassGenerationParams;
use crate::components::environment::{
    BiomeType, CardinalDirection, DesertType, ForestType, GrasslandType, Terrain, TerrainCell, TerrainPod, TerrainPodPhysicsFloor, TerrainPodPhysicsWall, TerrainProperties, TerrainType, WaterType
};
use crate::components::lifecycle::CurrentTerrainPod;
use crate::components::perception::{StimulusSource, StimulusSources, StimulusType};
use crate::components::GameAssets;
use crate::libraries::billboard::BillboardTexture;
use crate::materials::grass_instancing_material::GrassInstanceMaterial;
use crate::materials::grass_material::GrassMaterial;
use crate::materials::volumetric_grass_material::VolumetricGrassMaterial;
use crate::plugins::camera_plugin::MainCamera;
use crate::resources::environment::{TerrainGrid, TerrainPodGrid};
use crate::resources::plants::{PlantAssets, Sprite<PERSON>ey};
use crate::systems::environment::render::get_biome_color;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::debug_render::DebugRender;
use avian3d::prelude::{Collider, RigidBody};
use bevy::prelude::*;
use bevy::render::mesh::MeshTag;
use bevy_sprite3d::{Sprite3dBuilder, Sprite3dParams};
use bevy_tween::bevy_time_runner::Repeat;
use bevy_tween::combinator::{sequence, tween, AnimationBuilderExt};
use bevy_tween::interpolate::Scale;
use bevy_tween::interpolation::EaseKind;
use bevy_tween::prelude::ComponentTween;
use bevy_tween::tween::{AnimationTarget, TargetComponent};
use noise::{NoiseFn, Perlin};
use rand::{random, thread_rng, Rng};
use std::time::Duration;

const ELEVATION_MULTIPLIER: f32 = 1.0;

/// Parameters for terrain generation
pub struct TerrainGenerationParams {
    /// The width of each pod in cells
    pub pod_width: i32,
    /// The depth of each pod in cells
    pub pod_depth: i32,
    /// The size of each cell in the grid
    pub cell_size: f32,
    /// The seed for the noise generator
    pub seed: u32,
    /// The scale of the elevation noise
    pub elevation_scale: f64,
    /// The scale of the moisture noise
    pub moisture_scale: f64,
    /// The water level threshold (elevation below this is water)
    /// The scale of the temperature noise
    pub temperature_scale: f64,
    pub water_level: f32,
    /// The mountain level threshold (elevation above this is mountains)
    pub mountain_level: f32,
}

impl TerrainGenerationParams {
    pub fn get_terrain_height(&self) -> f32 {
        self.cell_size * 10.0 + self.cell_size / 2.0
    }
}

impl Default for TerrainGenerationParams {
    fn default() -> Self {
        let mut rng = thread_rng();
        Self {
            pod_width: 50,
            pod_depth: 50,
            cell_size: 1.0,
            seed: rng.gen(),
            elevation_scale: 0.02,
            moisture_scale: 0.05,
            temperature_scale: 0.01,
            water_level: 0.3,
            mountain_level: 0.7,
        }
    }
}

/// Generate a single terrain pod
pub fn generate_terrain_pod(
    mut commands: Commands,
    pod_index: (i32, i32),
    terrain_system_params: &mut TerrainSystemParams,
    terrain_generation_params: &TerrainGenerationParams,
    pod_grid: &mut TerrainPodGrid,
) -> Entity {
    info!("Generating terrain pod at ({}, {})...", pod_index.0, pod_index.1);

    // Create noise generators with different seeds
    let base_seed = terrain_generation_params.seed.wrapping_add(
        (pod_index.0 as u32)
            .wrapping_mul(10000)
            .wrapping_add((pod_index.1 as u32).wrapping_mul(1000)),
    );
    let elevation_noise = Perlin::new(base_seed);
    let moisture_noise = Perlin::new(base_seed.wrapping_add(1));
    let temperature_noise = Perlin::new(base_seed.wrapping_add(2));

    // Create a terrain grid for this pod
    let mut terrain_grid = TerrainGrid::new_for_pod(
        terrain_generation_params.pod_width,
        terrain_generation_params.pod_depth,
        terrain_generation_params.cell_size,
        pod_index,
    );

    let mut terrain_pod = TerrainPod {
        grid_index: pod_index,
        width: terrain_generation_params.pod_width,
        depth: terrain_generation_params.pod_depth,
        cell_size: terrain_generation_params.cell_size,
        neighbors: [None, None, None, None],
        terrain_grid: Some(terrain_grid.clone()),
    };

    // Create the pod entity
    let mut pod_translation = pod_grid.cell_to_world(pod_index.0, pod_index.1);
    // Add cell_size / 2 to the pod translation to center it
    pod_translation.y = 0.0;
    pod_translation.x += terrain_generation_params.cell_size / 2.;
    pod_translation.z += terrain_generation_params.cell_size / 2.;

    let pod_position = Transform::from_translation(pod_translation);
    log::info!("Pod position: {:?}", pod_position.translation);

    let pod_entity = commands
        .spawn((
            Name::new(format!("Terrain Pod ({}, {})", pod_index.0, pod_index.1)),
            terrain_pod.clone(),
            InheritedVisibility::default(),
            pod_position.clone(),
        ))
        .id();

    // Add the pod to the grid
    pod_grid.add_pod(pod_index.0, pod_index.1, pod_entity);

    // Decide if this pod will have a lake (25% chance)
    let mut rng = thread_rng();
    let will_have_lake = rng.gen_bool(1.0);

    // If we'll have a lake, determine its size and position before generating terrain
    let lake_info = if will_have_lake {
        // Choose lake size as a power of 2 (2, 4, 8, 16...)
        // Limit the maximum size to ensure it fits within the pod
        let max_power = (terrain_generation_params
            .pod_width
            .min(terrain_generation_params.pod_depth)
            / 2)
        .ilog2()
        .min(4) as u32;
        let lake_size = 2_i32.pow(rng.gen_range(1..=max_power));

        // Choose random starting position ensuring the lake fits within the pod
        let start_x =
            rng.gen_range(0..terrain_generation_params.pod_width - lake_size + 1);
        let start_z =
            rng.gen_range(0..terrain_generation_params.pod_depth - lake_size + 1);

        info!(
            "Planning lake of size {} at position ({}, {})",
            lake_size, start_x, start_z
        );

        Some((start_x, start_z, lake_size))
    } else {
        None
    };

    // Generate terrain cells for this pod
    for x in 0..terrain_generation_params.pod_width {
        for z in 0..terrain_generation_params.pod_depth {
            // Check if this cell is part of the lake
            let is_lake_cell = if let Some((lake_x, lake_z, lake_size)) = lake_info {
                x >= lake_x
                    && x < lake_x + lake_size
                    && z >= lake_z
                    && z < lake_z + lake_size
            } else {
                false
            };

            // Calculate global coordinates for noise generation
            let global_x = pod_index.0 * terrain_generation_params.pod_width + x;
            let global_z = pod_index.1 * terrain_generation_params.pod_depth + z;

            // Generate noise values
            let nx = global_x as f64 * terrain_generation_params.elevation_scale;
            let nz = global_z as f64 * terrain_generation_params.elevation_scale;

            // Get elevation from noise (range -1 to 1) and normalize to 0 to 1
            let elevation = (elevation_noise.get([nx, nz]) as f32 + 1.0) / 2.0;

            // Get moisture from a different noise function
            let moisture = (moisture_noise.get([
                nx * terrain_generation_params.moisture_scale,
                nz * terrain_generation_params.moisture_scale,
            ]) as f32
                + 1.0)
                / 2.0;

            // Get temperature from another noise function
            let temperature = (temperature_noise.get([
                nx * terrain_generation_params.temperature_scale,
                nz * terrain_generation_params.temperature_scale,
            ]) as f32
                + 1.0)
                / 2.0;

            // Determine terrain type and biome
            let (mut terrain_type, mut biome);

            if is_lake_cell {
                // Override with lake values if this cell is part of the lake
                let lake_type = WaterType::Lake;
                terrain_type = TerrainType::Water(lake_type);
                biome = BiomeType::Water(lake_type);
            } else {
                // Normal terrain determination if not a lake cell
                (terrain_type, biome) = determine_terrain_and_biome(
                    elevation,
                    moisture,
                    temperature,
                    terrain_generation_params.water_level,
                    terrain_generation_params.mountain_level,
                );
            }

            // Create terrain properties based on the terrain type and biome
            let properties =
                create_terrain_properties(terrain_type, biome, moisture, temperature);

            // Spawn a terrain entity
            let entity = commands
                .spawn((
                    Name::new(format!(
                        "Terrain Cell ({}, {}) in Pod ({}, {})",
                        x, z, pod_index.0, pod_index.1
                    )),
                    Terrain {
                        terrain_type,
                        biome,
                        properties,
                    },
                    TerrainCell {
                        x,
                        z,
                        elevation: elevation * ELEVATION_MULTIPLIER, // Scale elevation for visualization
                        pod_index,
                    },
                    CurrentTerrainPod::new(pod_entity, pod_index),
                    Transform::from_translation(terrain_grid.grid_to_world(x, z)),
                ))
                .id();

            // Add the entity to the terrain grid
            terrain_grid.cells.insert((x, z), entity);

            // Set stimulus sources for water
            if matches!(terrain_type, TerrainType::Water(_)) {
                let mut stimulus_sources = StimulusSources::new();

                // Water has a strong smell stimulus
                stimulus_sources.add(StimulusSource::new(StimulusType::Smell, 0.9));

                // Optionally add visual stimulus for water
                stimulus_sources.add(StimulusSource::new(StimulusType::Visual, 0.8));

                commands.entity(entity).insert(stimulus_sources);
            }

            // Spawn grass on grassland
            if matches!(biome, BiomeType::Grassland(_)) {
                let grass_height_seed = random::<u32>();

                // Set grass width based on GrasslandType
                let grass_width_base = match biome {
                    BiomeType::Grassland(GrasslandType::Savanna) => 0.03,
                    BiomeType::Grassland(GrasslandType::Prairie) => 0.025,
                    BiomeType::Grassland(GrasslandType::Steppe) => 0.02,
                    BiomeType::Grassland(GrasslandType::Meadow) => 0.035,
                    _ => 0.025,
                };

                // Set grass density based on GrasslandType
                let grass_density = match biome {
                    BiomeType::Grassland(GrasslandType::Savanna) => 10,
                    BiomeType::Grassland(GrasslandType::Prairie) => 12,
                    BiomeType::Grassland(GrasslandType::Steppe) => 8,
                    BiomeType::Grassland(GrasslandType::Meadow) => 15,
                    _ => 10,
                };

                let grass_width = (grass_width_base / 2.) + random::<f32>() * 0.005;
                let grass_height = grass_width * 2.;
                let grass_color = get_biome_color(&biome);

                let grass_patch =
                    terrain_system_params.generate_grass(GrassGenerationParams {
                        density: grass_density * 10,
                        grass_height,
                        grass_width,
                        tile_index: (x, z),
                        grass_base_color: grass_color.to_srgba().to_f32_array(),
                        grass_second_color: grass_color.to_srgba().to_f32_array(),
                        pod_width: terrain_generation_params.pod_width,
                        pod_depth: terrain_generation_params.pod_depth,
                        tile_size: terrain_generation_params.cell_size,
                        height_seed: grass_height_seed,
                        ..default()
                    });

                let target_scale = 0.98;
                let end_scale = Vec3::new(target_scale, 1., target_scale);
                let marker = TargetComponent::marker();
                let tween_forward = ComponentTween::new_target(
                    marker.clone(),
                    Scale {
                        start: Vec3::splat(1.),
                        end: end_scale,
                    },
                );

                let tween_backward = ComponentTween::new_target(
                    marker,
                    Scale {
                        start: end_scale,
                        end: Vec3::splat(1.),
                    },
                );

                let duration = 8.0 + random::<f32>() * 0.5;

                commands
                    .entity(grass_patch)
                    .insert(Transform::from_xyz(
                        0.0,
                        -terrain_generation_params.cell_size - 0.05,
                        0.0,
                    ))
                    .insert(AnimationTarget)
                    .animation()
                    .repeat(Repeat::Infinitely)
                    .insert(sequence((
                        tween(
                            Duration::from_secs_f32(duration),
                            EaseKind::BounceInOut,
                            tween_forward,
                        ),
                        tween(
                            Duration::from_secs_f32(duration),
                            EaseKind::BounceInOut,
                            tween_backward,
                        ),
                    )));

                commands
                    .entity(entity)
                    .insert(InheritedVisibility::default())
                    .add_child(grass_patch);
            }

            // Add the entity to the pod's entity set
            if let Ok(mut pod) = commands.get_entity(pod_entity) {
                pod.add_child(entity);
            }
        }
    }

    terrain_pod.terrain_grid = Some(terrain_grid);
    commands.entity(pod_entity).insert(terrain_pod.clone());

    // Spawn an invisible floor (that extends past the edge of the pod) for physics to act as the ground
    commands
        // .spawn(Transform::default())
        .spawn(pod_position.with_translation(Vec3::new(0.0, -0.01, 0.0)))
        .insert(TerrainPodPhysicsFloor)
        .insert(Name::new("Pod Physics Floor"))
        .insert(InheritedVisibility::default())
        .insert(RigidBody::Static)
        .insert(Collider::cuboid(100.0, 0.01, 100.0))
        .insert(DebugRender::default().without_axes().without_collider())
        .insert(ChildOf(pod_entity));

    // Spawn physics walls on each side of the pod
    let wall_thickness = 0.1;
    let wall_height = 2.0;
    let position_offset = 0.3;
    log::info!(
        "Pod width: {:?} | Pod depth: {:?}",
        terrain_generation_params.pod_width as f32 * terrain_generation_params.cell_size,
        terrain_generation_params.pod_depth as f32 * terrain_generation_params.cell_size
    );

    let wall_positions_and_size = [
        // Left Wall
        (
            Vec3::new(-position_offset, 0.0, 1.0),
            Vec3::new(
                wall_thickness,
                wall_height,
                terrain_generation_params.pod_depth as f32
                    * terrain_generation_params.cell_size,
            ),
        ),
        // Right Wall
        (
            Vec3::new(
                (terrain_generation_params.pod_width as f32
                    * terrain_generation_params.cell_size)
                    + wall_thickness
                    - position_offset,
                0.0,
                1.0,
            ),
            Vec3::new(
                wall_thickness,
                wall_height,
                terrain_generation_params.pod_depth as f32
                    * terrain_generation_params.cell_size,
            ),
        ),
        // Back Wall
        (
            Vec3::new(1.0, 0.0, -position_offset),
            Vec3::new(
                terrain_generation_params.pod_width as f32
                    * terrain_generation_params.cell_size,
                wall_height,
                wall_thickness,
            ),
        ),
        // Front Wall
        (
            Vec3::new(
                1.0,
                0.0,
                (terrain_generation_params.pod_depth as f32
                    * terrain_generation_params.cell_size)
                    + wall_thickness
                    - position_offset,
            ),
            Vec3::new(
                terrain_generation_params.pod_width as f32
                    * terrain_generation_params.cell_size,
                wall_height,
                wall_thickness,
            ),
        ),
    ];

    for (position, size) in wall_positions_and_size {
        commands
            .spawn(Transform::from_translation(
                position.with_y(1.0 + terrain_generation_params.cell_size / 2.),
            ))
            .insert(Name::new("Pod Physics Wall"))
            .insert(TerrainPodPhysicsWall)
            .insert(InheritedVisibility::default())
            .insert(RigidBody::Static)
            .insert(Collider::cuboid(size.x, size.y, size.z))
            .insert(DebugRender::default().without_axes().without_collider())
            .insert(ChildOf(pod_entity));
    }

    info!(
        "Terrain pod generation complete. Generated {}x{} terrain cells for pod ({}, {}).",
        terrain_generation_params.pod_width, terrain_generation_params.pod_depth, pod_index.0, pod_index.1
    );

    // Log lake information if one was created
    if let Some((lake_x, lake_z, lake_size)) = lake_info {
        info!(
            "Generated lake of size {} at position ({}, {})",
            lake_size, lake_x, lake_z
        );
    }

    pod_entity
}

pub fn on_physics_floor_added(
    mut commands: Commands,
    mut query: Query<Entity, Added<TerrainPodPhysicsFloor>>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for entity in query.iter() {
        commands
            .entity(entity)
            // .insert(Mesh3d(meshes.add(Rectangle::new(200.0, 1.0))))
            .insert(Mesh3d(meshes.add(Cuboid::new(5., 1., 5.))))
            .insert(MeshMaterial3d(materials.add(StandardMaterial {
                base_color: Color::srgba(1.0, 0.0, 0.0, 1.0),
                unlit: true,
                ..default()
            })));
    }
}

/// Generate multiple terrain pods to form a world
pub fn generate_terrain_world(
    mut commands: &mut Commands,
    mut terrain_system_params: &mut TerrainSystemParams,
    generation_params: TerrainGenerationParams,
    world_size: (i32, i32), // Number of pods in x and z directions
) {
    info!("Generating terrain world with {} x {} pods...", world_size.0, world_size.1);

    // Initialize the pod grid
    let mut pod_grid = TerrainPodGrid::new(
        (generation_params.pod_width, generation_params.pod_depth),
        generation_params.cell_size,
    );

    // Generate pods
    let mut pod_entities = Vec::new();

    for grid_x in 0..world_size.0.max(1) {
        for grid_z in 0..world_size.1.max(1) {
            let pod_entity = generate_terrain_pod(
                commands.reborrow(),
                (grid_x, grid_z),
                &mut terrain_system_params,
                &generation_params,
                &mut pod_grid,
            );

            pod_entities.push(((grid_x, grid_z), pod_entity));
        }
    }

    // Connect neighboring pods
    for ((grid_x, grid_z), entity) in pod_entities.iter() {
        if let Ok((mut pod, _)) = terrain_system_params.terrain_pod_query.get_mut(*entity)
        {
            // Check all four directions
            for direction in [
                CardinalDirection::North,
                CardinalDirection::East,
                CardinalDirection::South,
                CardinalDirection::West,
            ] {
                let (dx, dz) = direction.offset();
                let neighbor_x = *grid_x + dx;
                let neighbor_z = *grid_z + dz;

                if neighbor_x >= 0
                    && neighbor_x < world_size.0
                    && neighbor_z >= 0
                    && neighbor_z < world_size.1
                {
                    if let Some(neighbor_entity) =
                        pod_grid.get_pod(neighbor_x, neighbor_z)
                    {
                        pod.neighbors[direction.index()] = Some(*neighbor_entity);
                    }
                }
            }
        }
    }

    // Set the first pod as active
    if world_size.0 > 0 && world_size.1 > 0 {
        pod_grid.set_active_pod(0, 0);
    }

    *terrain_system_params.terrain_pod_grid = pod_grid;

    info!(
        "Terrain world generation complete. Generated {} pods.",
        world_size.0 * world_size.1
    );
}

pub fn set_pod_neighbors(
    mut terrain_pod: Query<&mut TerrainPod, Changed<TerrainPod>>,
    pod_grid: Res<TerrainPodGrid>,
) {
    for (&pod_index, &pod_entity) in pod_grid.pods.iter() {
        if let Ok(mut pod) = terrain_pod.get_mut(pod_entity) {
            // Check all four directions
            for direction in [
                CardinalDirection::North,
                CardinalDirection::East,
                CardinalDirection::South,
                CardinalDirection::West,
            ] {
                let (dx, dz) = direction.offset();
                let neighbor_x = pod_index.0 + dx;
                let neighbor_z = pod_index.1 + dz;

                if let Some(neighbor_entity) = pod_grid.get_pod(neighbor_x, neighbor_z) {
                    pod.neighbors[direction.index()] = Some(*neighbor_entity);
                }
            }
        }
    }
}

/// Determine the terrain type and biome based on elevation, moisture, and temperature
fn determine_terrain_and_biome(
    elevation: f32,
    moisture: f32,
    temperature: f32,
    water_level: f32,
    mountain_level: f32,
) -> (TerrainType, BiomeType) {
    // Water biomes
    if elevation < water_level {
        let water_type = if elevation < water_level * 0.5 {
            WaterType::Ocean
        } else if moisture > 0.7 {
            WaterType::Swamp
        } else if moisture > 0.5 {
            WaterType::Lake
        } else {
            WaterType::River
        };

        return (TerrainType::Water(water_type), BiomeType::Water(water_type));
    }

    // Mountain biomes
    if elevation > mountain_level {
        return (TerrainType::Rock, BiomeType::Mountain);
    }

    // Desert biomes (hot and dry)
    if temperature > 0.7 && moisture < 0.3 {
        let desert_type = if temperature > 0.8 {
            DesertType::Hot
        } else if elevation > 0.6 {
            DesertType::Rocky
        } else {
            DesertType::Sandy
        };

        return (TerrainType::Sand, BiomeType::Desert(desert_type));
    }

    // Tundra (cold)
    if temperature < 0.3 {
        return (
            TerrainType::Soil(crate::components::environment::SoilType::Peat),
            BiomeType::Tundra,
        );
    }

    // Forest biomes (medium to high moisture)
    if moisture > 0.5 {
        let forest_type = if temperature > 0.7 {
            if moisture > 0.8 {
                ForestType::Rainforest
            } else {
                ForestType::Tropical
            }
        } else if temperature < 0.4 {
            ForestType::Boreal
        } else {
            ForestType::Temperate
        };

        return (
            TerrainType::Soil(crate::components::environment::SoilType::Loam),
            BiomeType::Forest(forest_type),
        );
    }

    // Grassland biomes (default)
    let grassland_type = if temperature > 0.6 {
        if moisture > 0.4 {
            GrasslandType::Savanna
        } else {
            GrasslandType::Steppe
        }
    } else if moisture > 0.4 {
        GrasslandType::Prairie
    } else {
        GrasslandType::Meadow
    };

    (
        TerrainType::Soil(crate::components::environment::SoilType::Loam),
        BiomeType::Grassland(grassland_type),
    )
}

/// Create terrain properties based on the terrain type and biome
fn create_terrain_properties(
    terrain_type: TerrainType,
    biome: BiomeType,
    moisture: f32,
    temperature: f32,
) -> TerrainProperties {
    match terrain_type {
        TerrainType::Soil(soil_type) => {
            let mut properties = TerrainProperties::from_soil_type(soil_type);

            // Adjust properties based on biome
            match biome {
                BiomeType::Forest(forest_type) => {
                    properties.fertility = 0.8;
                    properties.moisture = moisture * 1.2;

                    match forest_type {
                        ForestType::Rainforest => {
                            properties.moisture = 0.9;
                            properties.fertility = 0.9;
                        }
                        ForestType::Tropical => {
                            properties.temperature = 30.0;
                            properties.moisture = 0.8;
                        }
                        ForestType::Boreal => {
                            properties.temperature = 5.0;
                            properties.moisture = 0.6;
                        }
                        ForestType::Temperate => {
                            properties.temperature = 15.0;
                            properties.moisture = 0.7;
                        }
                    }
                }
                BiomeType::Grassland(grassland_type) => {
                    properties.fertility = 0.7;

                    match grassland_type {
                        GrasslandType::Savanna => {
                            properties.temperature = 28.0;
                            properties.moisture = 0.4;
                        }
                        GrasslandType::Prairie => {
                            properties.temperature = 20.0;
                            properties.moisture = 0.5;
                        }
                        GrasslandType::Steppe => {
                            properties.temperature = 15.0;
                            properties.moisture = 0.3;
                        }
                        GrasslandType::Meadow => {
                            properties.temperature = 18.0;
                            properties.moisture = 0.6;
                        }
                    }
                }
                BiomeType::Tundra => {
                    properties.temperature = 0.0;
                    properties.moisture = 0.4;
                    properties.fertility = 0.3;
                }
                _ => {}
            }

            properties
        }
        TerrainType::Water(_) => TerrainProperties {
            water_retention: 1.0,
            moisture: 1.0,
            fertility: 0.2,
            ph: 7.0,
            temperature: 15.0 + temperature * 10.0,
            ..Default::default()
        },
        TerrainType::Rock => TerrainProperties {
            water_retention: 0.1,
            moisture: moisture * 0.5,
            fertility: 0.1,
            ph: 6.5,
            temperature: 10.0 + temperature * 15.0,
            ..Default::default()
        },
        TerrainType::Sand => TerrainProperties {
            water_retention: 0.2,
            moisture: moisture * 0.3,
            fertility: 0.2,
            ph: 8.0,
            temperature: 25.0 + temperature * 15.0,
            ..Default::default()
        },
    }
}
