use crate::components::environment::{
    BiomeType, DesertType, ForestType, GrasslandType, Terrain, WaterType, WaterVolume
};
use crate::post_process::pixelated_material::{
    ExtendedPixelatedMaterial, PixelatedMaterial
};
use crate::resources::environment::TerrainPodGrid;
use avian3d::prelude::{Collider, DebugRender, Friction, RigidBody};
use bevy::gltf::GltfMaterialName;
use bevy::pbr::ExtendedMaterial;
use bevy::prelude::*;
use bevy::render::render_resource::Face;

/// System to visualize terrain biomes with different colors
pub fn render_biomes(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<ExtendedPixelatedMaterial>>,
    terrain_pod_grid: Res<TerrainPodGrid>,
    query: Query<(Entity, &Terrain, &Transform), Added<Terrain>>,
) {
    for (entity, terrain, transform) in query.iter() {
        // Create a simple cube mesh for each terrain cell
        let size = terrain_pod_grid.cell_size;
        let cube = Mesh::from(Cuboid::new(size, size, size));

        let mut material = PixelatedMaterial::simple(get_biome_color(&terrain.biome));

        let mut friction = Friction::new(1.0);

        // If the biome is water, make it transparent
        if matches!(terrain.biome, BiomeType::Water(_)) {
            material.base.base_color = material.base.base_color.with_alpha(0.5);
            material.base.alpha_mode = AlphaMode::Blend;
            material.base.double_sided = false;
            material.base.ior = 1.33;
            material.base.perceptual_roughness = 0.9;
            material.base.metallic = 0.0;
            material.base.reflectance = 0.1;
            material.base.thickness = 1.0;
            material.base.unlit = true;
            material.base.cull_mode = Some(Face::Back);

            // Add water friction
            friction = Friction::new(0.1);

            commands.entity(entity).insert(WaterVolume {
                density: match terrain.biome {
                    BiomeType::Water(WaterType::Ocean) => 1.03,
                    BiomeType::Water(WaterType::Swamp) => 1.05,
                    _ => 1.0,
                },
            });
        }

        // Add components to visualize the terrain
        commands.entity(entity).insert((
            Mesh3d(meshes.add(cube)),
            MeshMaterial3d(materials.add(material)),
            friction,
            RigidBody::Static,
            Collider::cuboid(size, size, size),
            DebugRender::default()
                .without_axes()
                .without_aabb()
                .without_collider(),
            *transform,
        ));
    }
}

pub fn set_rock_asset_materials(
    mut commands: Commands,
    mut materials: ResMut<Assets<ExtendedPixelatedMaterial>>,
    query: Query<(Entity, &GltfMaterialName), Added<MeshMaterial3d<StandardMaterial>>>,
) {
    for (entity, material_name) in query.iter() {
        let material_name = material_name.0.to_lowercase();
        let is_rock = material_name.contains("rock");

        if !is_rock {
            continue;
        }

        commands
            .entity(entity)
            .remove::<MeshMaterial3d<StandardMaterial>>()
            // Rock material
            .insert(MeshMaterial3d(materials.add(ExtendedMaterial {
                base: StandardMaterial {
                    base_color: Color::WHITE,
                    perceptual_roughness: 0.9,
                    metallic: 0.0,
                    reflectance: 0.1,
                    ..default()
                },
                extension: PixelatedMaterial { quantize_steps: 4 },
            })));
    }
}

/// Get a color for a biome type
pub fn get_biome_color(biome: &BiomeType) -> Color {
    match biome {
        BiomeType::Forest(forest_type) => match forest_type {
            ForestType::Temperate => Color::srgb(0.0, 0.5, 0.0), // Medium green
            ForestType::Tropical => Color::srgb(0.0, 0.7, 0.0),  // Bright green
            ForestType::Boreal => Color::srgb(0.0, 0.4, 0.2),    // Dark green
            ForestType::Rainforest => Color::srgb(0.0, 0.8, 0.4), // Vibrant green
        },
        BiomeType::Desert(desert_type) => match desert_type {
            DesertType::Hot => Color::srgb(0.9, 0.8, 0.2), // Yellow
            DesertType::Cold => Color::srgb(0.8, 0.8, 0.6), // Light tan
            DesertType::Rocky => Color::srgb(0.6, 0.5, 0.4), // Brown
            DesertType::Sandy => Color::srgb(0.9, 0.8, 0.5), // Sand color
        },
        BiomeType::Grassland(grassland_type) => match grassland_type {
            GrasslandType::Savanna => Color::srgb(0.8, 0.7, 0.2), // Yellow-green
            GrasslandType::Prairie => Color::srgb(0.4, 0.8, 0.2), // Light green
            GrasslandType::Steppe => Color::srgb(0.7, 0.7, 0.3),  // Olive
            GrasslandType::Meadow => Color::srgb(0.4, 0.8, 0.4),  // Bright green
        },
        BiomeType::Tundra => Color::srgb(0.8, 0.9, 0.9), // Light blue-white
        BiomeType::Mountain => Color::srgb(0.5, 0.5, 0.5), // Gray
        BiomeType::Water(water_type) => match water_type {
            WaterType::Lake => Color::srgb(0.0, 0.3, 0.8), // Blue
            WaterType::River => Color::srgb(0.0, 0.5, 0.9), // Light blue
            WaterType::Ocean => Color::srgb(0.0, 0.1, 0.6), // Dark blue
            WaterType::Swamp => Color::srgb(0.2, 0.3, 0.1), // Dark green-brown
        },
    }
}
