use crate::components::environment::{
    SoilType, Terrain, TerrainEvent, TerrainEventType, TerrainType
};
use crate::components::plants::Plant;
use crate::resources::environment::{
    Climate, EnvironmentEvents, GlobalEventType, TerrainPodGrid, WaterSystem
};
use crate::resources::{DayPhase, TimeOfDay, WorldSimulationDeltaTime};
use bevy::prelude::*;

/// System to update terrain moisture levels based on environmental factors
pub fn update_terrain_moisture(
    mut terrain_query: Query<&mut Terrain>,
    delta_time: Res<WorldSimulationDeltaTime>,
    climate: Res<Climate>,
    water_system: Option<Res<WaterSystem>>,
    time_of_day: Res<TimeOfDay>,
) {
    // Base evaporation rate depends on temperature
    let base_evaporation_rate = 0.001 * (climate.base_temperature / 80.0).max(0.1);

    // Rainfall contribution - higher during certain times of day or during rainfall events
    let rainfall_contribution = match time_of_day.phase() {
        DayPhase::Morning => climate.rainfall * 0.2, // Some morning dew
        DayPhase::Afternoon => climate.rainfall * 0.1, // Less in afternoon
        DayPhase::Evening => climate.rainfall * 0.3, // More in evening
        DayPhase::Night => climate.rainfall * 0.05,   // Less at night
    };

    // Water table contribution if available
    let water_table_level = water_system.map_or(0.0, |ws| ws.water_table * 0.01);

    for mut terrain in terrain_query.iter_mut() {
        let properties = &mut terrain.properties;

        // Calculate moisture change based on environmental factors
        let mut moisture_change = 0.0;

        // Add rainfall contribution
        moisture_change += rainfall_contribution;

        // Add water table contribution
        moisture_change += water_table_level;

        // Subtract evaporation (affected by temperature and water retention)
        let evaporation =
            base_evaporation_rate * (1.0 - properties.water_retention * 0.5);
        moisture_change -= evaporation;

        // Apply moisture change, but limit by water retention capacity
        properties.moisture += moisture_change * delta_time.0;
        properties.moisture = properties.moisture.clamp(0.0, properties.water_retention);
    }
}

/// System to update terrain fertility based on environmental factors
pub fn update_terrain_fertility(
    mut terrain_query: Query<(&mut Terrain, &Transform)>,
    plants_query: Query<&Transform, With<Plant>>,
    time: Res<WorldSimulationDeltaTime>,
    pods_grid: Res<TerrainPodGrid>,
) {
    // Base fertility change rate (very slow natural changes)
    let base_fertility_change_rate = 0.001;

    // Create a map of plant positions to terrain cells for faster lookup
    let mut plant_positions = Vec::new();
    for plant_transform in plants_query.iter() {
        plant_positions.push(plant_transform.translation);
    }

    for (mut terrain, transform) in terrain_query.iter_mut() {
        let terrain_type = &terrain.terrain_type;
        let terrain_pos = transform.translation;

        // Calculate fertility change based on environmental factors
        let mut fertility_change = 0.0;

        // Natural fertility change (very slow)
        match terrain_type {
            TerrainType::Soil(SoilType::Peat) => {
                // Peat naturally increases fertility slowly
                fertility_change += base_fertility_change_rate * 2.0;
            }
            TerrainType::Soil(_) => {
                // Other soils have a small natural increase
                fertility_change += base_fertility_change_rate;
            }
            TerrainType::Water(_) => {
                // Water bodies maintain their fertility
                fertility_change += 0.0;
            }
            _ => {
                // Other terrain types slowly lose fertility
                fertility_change -= base_fertility_change_rate;
            }
        }

        // Plants contribute to fertility over time (organic matter)
        for plant_pos in &plant_positions {
            let distance = terrain_pos.distance(*plant_pos);
            if distance < pods_grid.cell_size * 2.0 {
                // Plants nearby contribute to fertility
                fertility_change += 0.002 * time.0;
            }
        }

        // Moisture affects fertility - too dry or too wet reduces fertility
        let moisture_factor = 1.0 - (terrain.properties.moisture - 0.5).abs() * 0.5;
        fertility_change *= moisture_factor;

        // Apply fertility change
        terrain.properties.fertility += fertility_change * time.0;
        terrain.properties.fertility = terrain.properties.fertility.clamp(0.1, 1.0);
    }
}

/// System to handle terrain events like rainfall or drought
pub fn handle_terrain_events(
    mut terrain_query: Query<(&mut Terrain, &mut TerrainEvent)>,
    time: Res<WorldSimulationDeltaTime>,
) {
    for (mut terrain, mut event) in terrain_query.iter_mut() {
        // Update event timer
        event.time_remaining -= time.0;

        // Apply event effects
        match event.event_type {
            TerrainEventType::Rainfall => {
                // Increase moisture based on rainfall intensity
                let moisture_increase = event.intensity * 0.05 * time.0;
                terrain.properties.moisture += moisture_increase;
                terrain.properties.moisture = terrain
                    .properties
                    .moisture
                    .min(terrain.properties.water_retention);
            }
            TerrainEventType::Drought => {
                // Decrease moisture based on drought intensity
                let moisture_decrease = event.intensity * 0.03 * time.0;
                terrain.properties.moisture -= moisture_decrease;
                terrain.properties.moisture = terrain.properties.moisture.max(0.0);

                // Prolonged drought can reduce fertility
                if event.time_remaining < event.duration * 0.5 {
                    terrain.properties.fertility -= 0.001 * time.0;
                    terrain.properties.fertility = terrain.properties.fertility.max(0.1);
                }
            }
            TerrainEventType::Flood => {
                // Floods saturate the soil but can wash away nutrients
                terrain.properties.moisture = terrain.properties.water_retention;
                terrain.properties.fertility -= 0.002 * time.0;
                terrain.properties.fertility = terrain.properties.fertility.max(0.1);
            }
            TerrainEventType::Fire => {
                // Fires reduce moisture and temporarily reduce fertility
                terrain.properties.moisture -= 0.05 * time.0;
                terrain.properties.moisture = terrain.properties.moisture.max(0.0);

                // But ash can later increase fertility (after fire ends)
                if event.time_remaining <= 0.0 {
                    terrain.properties.fertility += 0.1;
                    terrain.properties.fertility = terrain.properties.fertility.min(1.0);
                }
            }
        }

        // Remove expired events
        if event.time_remaining <= 0.0 {
            // Event is handled in the parent entity system
        }
    }
}

/// System to handle global environment events
pub fn handle_global_environment_events(
    mut commands: Commands,
    mut terrain_query: Query<(Entity, &mut Terrain)>,
    global_events: Res<EnvironmentEvents>,
) {
    for event in global_events.active_events.iter() {
        match event.event_type {
            GlobalEventType::Rainfall => {
                // Apply rainfall to all terrain cells
                for (entity, mut terrain) in terrain_query.iter_mut() {
                    // Increase moisture based on rainfall intensity and water retention
                    let moisture_increase =
                        event.intensity * 0.1 * terrain.properties.water_retention;

                    terrain.properties.moisture += moisture_increase;
                    terrain.properties.moisture = terrain
                        .properties
                        .moisture
                        .min(terrain.properties.water_retention);

                    // Add a terrain event component if it doesn't exist
                    commands.entity(entity).insert(TerrainEvent {
                        event_type: TerrainEventType::Rainfall,
                        duration: event.duration,
                        intensity: event.intensity,
                        time_remaining: event.duration,
                    });
                }
            }
            GlobalEventType::Drought => {
                // Apply drought to all terrain cells
                for (entity, mut _terrain) in terrain_query.iter_mut() {
                    // Add a terrain event component
                    commands.entity(entity).insert(TerrainEvent {
                        event_type: TerrainEventType::Drought,
                        duration: event.duration,
                        intensity: event.intensity,
                        time_remaining: event.duration,
                    });
                }
            }
            GlobalEventType::HeatWave => {
                // Heat waves increase evaporation
                for (_entity, mut terrain) in terrain_query.iter_mut() {
                    // Increase temperature
                    terrain.properties.temperature += 10.0 * event.intensity;

                    // Decrease moisture due to evaporation
                    let moisture_decrease = event.intensity * 0.05;
                    terrain.properties.moisture -= moisture_decrease;
                    terrain.properties.moisture = terrain.properties.moisture.max(0.0);
                }
            }
            GlobalEventType::ColdSnap => {
                // Cold snaps decrease evaporation but can damage plants
                for (entity, mut terrain) in terrain_query.iter_mut() {
                    // Decrease temperature
                    terrain.properties.temperature -= 10.0 * event.intensity;
                }
            }
            GlobalEventType::Storm => {
                // Storms bring heavy rainfall but can cause erosion
                for (entity, mut terrain) in terrain_query.iter_mut() {
                    // Increase moisture significantly
                    let moisture_increase =
                        event.intensity * 0.2 * terrain.properties.water_retention;
                    terrain.properties.moisture += moisture_increase;
                    terrain.properties.moisture = terrain
                        .properties
                        .moisture
                        .min(terrain.properties.water_retention);

                    // But storms can wash away nutrients
                    terrain.properties.fertility -= 0.01 * event.intensity;
                    terrain.properties.fertility = terrain.properties.fertility.max(0.1);

                    // Add a terrain event component
                    commands.entity(entity).insert(TerrainEvent {
                        event_type: TerrainEventType::Rainfall,
                        duration: event.duration,
                        intensity: event.intensity * 1.5, // Storms are more intense than regular rainfall
                        time_remaining: event.duration,
                    });
                }
            }
            _ => {}
        }
    }
}
