use crate::components::environment::Sun;
use crate::events::time_of_day_events::{SunriseEvent, SunsetEvent};
use crate::resources::{DayPhase, TimeOfDay};
use crate::systems::environment::system_params::TerrainSystemParams;
use bevy::pbr::{CascadeShadowConfigBuilder, VolumetricFog, VolumetricLight};
use bevy::prelude::*;

/// Spawns the sun (directional light) if it doesn't exist
pub fn setup_sun(mut commands: Commands, query: Query<Entity, With<Sun>>) {
    if query.is_empty() {
        commands.spawn((
            Name::new("Sun"),
            Sun,
            VolumetricLight,
            CascadeShadowConfigBuilder {
                num_cascades: 3,
                maximum_distance: 10.0,
                overlap_proportion: 0.0,
                ..default()
            }
            .build(),
            DirectionalLight {
                illuminance: 10000.0,
                shadows_enabled: true,
                shadow_depth_bias: 0.,
                shadow_normal_bias: 15.0,
                color: Color::srgba(1.000, 0.898, 0.000, 1.000),
                ..default()
            },
            Transform::from_xyz(0.0, 50.0, 0.0).looking_at(Vec3::ZERO, Vec3::Z),
        ));
    }
}

/// Updates the sun's position and properties based on time of day
pub fn update_sun(
    time: Res<TimeOfDay>,
    terrain_pod_system: TerrainSystemParams,
    mut query: Query<(&mut Transform, &mut DirectionalLight), With<Sun>>,
) {
    if let Ok((mut transform, mut light)) = query.single_mut() {
        // Calculate the sun's position based on time of day
        // The sun should move in an arc from east to west

        // Get the day fraction (0.0 to 1.0)
        let day_fraction = time.fraction();

        // Calculate the angle of the sun (0 = east, π = west)
        let sun_angle = day_fraction * std::f32::consts::PI;

        // Calculate the height of the sun (highest at noon)
        let sun_height = (day_fraction * std::f32::consts::PI).sin() * 100.0;

        // Calculate the sun's position
        let x = 100.0 * sun_angle.cos();
        let y = sun_height.max(2.0); // Keep the sun at least slightly above the horizon
        let z = 100.0 * sun_angle.sin();

        // Update the sun's position
        transform.translation = Vec3::new(x, y, z);

        let active_pod_position = terrain_pod_system
            .get_center_of_active_pod()
            .unwrap_or_default();
        transform.look_at(active_pod_position, Vec3::Y);

        // Adjust the sun's brightness based on time of day
        match time.phase() {
            DayPhase::Morning => {
                // Gradually increase brightness during morning
                let morning_progress = (time.hour() - 6.0) / 6.0; // 0.0 at 6am, 1.0 at noon
                light.illuminance = 5000.0 + (morning_progress * 5000.0);
            }
            DayPhase::Afternoon => {
                // Full brightness during afternoon
                let afternoon_progress = (time.hour() - 12.0) / 6.0; // 0.0 at noon, 1.0 at 6pm
                light.illuminance = 10000.0 + (afternoon_progress * 1000.0);
            }
            DayPhase::Evening => {
                // Gradually decrease brightness during evening
                let evening_progress = (time.hour() - 18.0) / 6.0; // 0.0 at 6pm, 1.0 at midnight
                light.illuminance = 10000.0 - (evening_progress * 9000.0);
            }
            DayPhase::Night => {
                // Minimal brightness during night
                let night_progress = (time.hour() - 24.0) / 6.0; // 0.0 at midnight, 1.0 at 6am
                light.illuminance = 1000.0 - (night_progress * 900.0);
            }
        }
    }
}

/// Adjusts the ambient light based on time of day
pub fn update_ambient_light(
    time: Res<TimeOfDay>,
    mut ambient_light: ResMut<AmbientLight>,
) {
    // Adjust the ambient light color and brightness based on time of day
    match time.phase() {
        DayPhase::Morning => {
            // Warm, gradually brightening light during morning
            let morning_progress = (time.hour() - 6.0) / 6.0; // 0.0 at 6am, 1.0 at noon
            let brightness = 0.1 + (morning_progress * 0.2);
            ambient_light.color = Color::srgb(1.0, 0.9, 0.8); // Warm morning light
                                                              // ambient_light.brightness = brightness;
        }
        DayPhase::Afternoon => {
            // Bright, neutral light during afternoon
            let afternoon_progress = (time.hour() - 12.0) / 6.0; // 0.0 at noon, 1.0 at 6pm
            let brightness = 0.3 + (afternoon_progress * 0.2);
            ambient_light.color = Color::srgb(1.0, 1.0, 1.0); // Neutral daylight
                                                              // ambient_light.brightness = brightness;
        }
        DayPhase::Evening => {
            // Warm, gradually dimming light during evening
            let evening_progress = (time.hour() - 18.0) / 6.0; // 0.0 at 6pm, 1.0 at midnight
            let brightness = 0.3 - (evening_progress * 0.25);
            ambient_light.color = Color::srgb(1.0, 0.8, 0.6); // Warm evening light
                                                              // ambient_light.brightness = brightness;
        }
        DayPhase::Night => {
            // Cool, dim light during night
            let night_progress = (time.hour() - 24.0) / 6.0; // 0.0 at midnight, 1.0 at 6am
            let brightness = 0.05 + (night_progress * 0.05);
            ambient_light.color = Color::srgb(0.5, 0.5, 1.0); // Cool moonlight
                                                              // ambient_light.brightness = brightness;
        }
    }
}

/// Handles sunrise events
pub fn handle_sunrise(
    mut events: EventReader<SunriseEvent>,
    mut query: Query<&mut DirectionalLight, With<Sun>>,
) {
    for _ in events.read() {
        if let Ok(mut _light) = query.single_mut() {
            // Adjust the sun's properties at sunrise
            // light.shadows_enabled = true;
            info!("Sunrise started");
        }
    }
}

/// Handles sunset events
pub fn handle_sunset(
    mut events: EventReader<SunsetEvent>,
    mut query: Query<&mut DirectionalLight, With<Sun>>,
) {
    for _ in events.read() {
        if let Ok(_light) = query.single_mut() {
            // Adjust the sun's properties at sunset
            // We keep shadows enabled for moonlight shadows
            info!("Sunset started");
        }
    }
}
