use crate::components::animals::InWater;
use crate::components::environment::{BiomeType, Terrain, WaterType, WaterVolume};
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{Collider, LinearVelocity, Mass, RigidBody};
use bevy::prelude::*;

pub struct WaterPhysicsPlugin;

impl Plugin for WaterPhysicsPlugin {
    fn build(&self, app: &mut App) {
        app.add_systems(Update, apply_buoyancy_forces);
    }
}

/// System to apply buoyancy forces to entities in water
pub fn apply_buoyancy_forces(
    mut commands: Commands,
    water_query: Query<
        (&GlobalTransform, &Terrain, &WaterVolume),
        (With<Terrain>, With<Collider>),
    >,
    mut entity_query: Query<
        (Entity, &GlobalTransform, &Collider, &Mass, &mut LinearVelocity),
        (With<RigidBody>, Without<Terrain>),
    >,
    in_water_query: Query<Entity, With<InWater>>,
    delta_time: Res<WorldSimulationDeltaTime>,
    climate: Res<crate::resources::environment::Climate>,
) {
    for (entity, entity_transform, collider, mass, mut linear_velocity) in
        entity_query.iter_mut()
    {
        for (water_transform, terrain, water_volume) in water_query.iter() {
            // Skip non-water terrain
            if !matches!(terrain.biome, BiomeType::Water(_)) {
                continue;
            }

            let Some(cuboid) = collider.shape().as_cuboid() else {
                continue;
            };

            let half_extents: Vec3 = Vec3::new(
                cuboid.half_extents.x as f32,
                cuboid.half_extents.y as f32,
                cuboid.half_extents.z as f32,
            );

            // Check if entity is overlapping with water
            let entity_pos = entity_transform.translation();
            let water_pos = water_transform.translation();

            // Simple AABB overlap check - replace with proper collision detection if needed
            let water_top = water_pos.y + half_extents.y;
            let entity_bottom = entity_pos.y - half_extents.y;

            if entity_bottom <= water_top {
                if !in_water_query.contains(entity) {
                    commands.entity(entity).insert(InWater);
                }

                // Calculate submersion depth
                let submersion_depth = (water_top - entity_bottom).max(0.0);
                if submersion_depth <= 0.0 {
                    continue;
                }

                // Calculate buoyancy force based on water density
                let water_density = water_volume.density;

                // Apply buoyancy force (F = density * volume * g)
                // Simplified calculation - adjust as needed
                let volume = half_extents.element_product() * 8.0;
                let submerged_volume =
                    volume * (submersion_depth / (half_extents.y * 2.0));
                let buoyancy_force = water_density * submerged_volume * 9.81;

                // Calculate if entity should float or sink
                let mass_value = mass.0;
                let net_force = buoyancy_force - (mass_value * 9.81);

                // Apply force as velocity change
                linear_velocity.0.y += net_force * delta_time.0 / mass_value;

                // Add drag force in water
                let water_drag = match terrain.biome {
                    BiomeType::Water(WaterType::River) => 2.0, // Stronger drag in rivers
                    _ => 1.0,
                };

                linear_velocity.0.x *= 1.0 - (0.1 * water_drag * delta_time.0);
                linear_velocity.0.z *= 1.0 - (0.1 * water_drag * delta_time.0);

                // Apply wind force to the portion of the object above water
                let exposed_ratio =
                    1.0 - (submersion_depth / (half_extents.y * 2.0)).min(1.0);

                if exposed_ratio > 0.0 {
                    // Get wind direction and speed from climate
                    let wind_direction = climate.wind_direction.normalize_or_zero();
                    let wind_speed = climate.wind_speed;

                    // Calculate wind force based on exposed area and wind speed
                    // Simplified calculation - adjust as needed
                    let wind_force = wind_direction * wind_speed * exposed_ratio * 0.05;

                    // Apply wind force as horizontal velocity change
                    linear_velocity.0.x += wind_force.x * delta_time.0 / mass_value;
                    linear_velocity.0.z += wind_force.y * delta_time.0 / mass_value;

                    // Add wave effect based on wind (subtle vertical oscillation)
                    if wind_speed > 0.5 {
                        let wave_strength = wind_speed * 0.02 * exposed_ratio;
                        linear_velocity.0.y += wave_strength
                            * (std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap()
                                .as_secs_f32()
                                .sin())
                            * delta_time.0;
                    }
                }
            } else {
                if in_water_query.contains(entity) {
                    commands.entity(entity).remove::<InWater>();
                }
            }
        }
    }
}
