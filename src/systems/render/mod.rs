use crate::components::Billboard;
use crate::post_process::pixelated_material::{
    ExtendedPixelatedMaterial, PixelatedMaterial
};
use bevy::gltf::GltfMaterialName;
use bevy::pbr::ExtendedMaterial;
use bevy::prelude::*;

const MIN_PITCH: f32 = -0.5;
const MAX_PITCH: f32 = 0.5;

/// System to update billboards to always face the camera
pub fn update_billboards(
    camera: Query<
        &Transform,
        (With<crate::plugins::camera_plugin::MainCamera>, Without<Billboard>),
    >,
    mut query: Query<(&GlobalTransform, &mut Transform), With<Billboard>>,
) {
    if let Ok(cam_tf) = camera.single() {
        for (_, mut tf) in &mut query {
            let look_dir = (cam_tf.translation - tf.translation).normalize();
            tf.look_to(look_dir, Vec3::Y);
        }
    }
}

pub fn set_pixelated_materials(
    mut commands: Commands,
    standard_materials: Res<Assets<StandardMaterial>>,
    mut materials: ResMut<Assets<ExtendedPixelatedMaterial>>,
    query: Query<
        (Entity, &MeshMaterial3d<StandardMaterial>, &GltfMaterialName, Option<&MeshMaterial3d<ExtendedPixelatedMaterial>>),
        Added<MeshMaterial3d<StandardMaterial>>,
    >,
) {
    for (entity, handle, _material_name, pixelated_material) in query.iter() {
        if pixelated_material.is_some() {
            continue;
        }

        let Some(standard_material) = standard_materials.get(handle) else {
            continue;
        };

        commands
            .entity(entity)
            .remove::<MeshMaterial3d<StandardMaterial>>()
            .insert(MeshMaterial3d(materials.add(ExtendedMaterial {
                base: standard_material.clone(),
                extension: PixelatedMaterial { quantize_steps: 8 },
            })));
    }
}
