use crate::components::ai::neural_network::{BaseInputType, NeuralBrain};
use crate::components::lifecycle::{BiologicalOrganism, Hunger, Stamina, Thirst};
use crate::resources::{TimeOfDay, WorldSimulationDeltaTime};
use bevy::prelude::*;
use rand::Rng;

pub fn update_neural_inputs(
    mut query: Query<
        (&mut NeuralBrain, &Thirst, &Hunger, &Stamina, &Transform),
        With<BiologicalOrganism>,
    >,
    time_of_day: Res<TimeOfDay>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    let mut rng = rand::thread_rng();

    for (mut brain, thirst, hunger, stamina, transform) in query.iter_mut() {
        brain.last_update += delta_time.0;
        if brain.last_update < brain.update_frequency {
            continue;
        }

        // Reset update timer
        brain.last_update = 0.0;

        // Update inputs based on the new input schema (length = 12)

        // 0: thirst (0.0 = dehydrated, 1.0 = hydrated)
        brain.set_base_input(BaseInputType::Thirst, thirst.normalized_thirst() as f64);

        // 1: hunger (0.0 = starving, 1.0 = full)
        brain.set_base_input(BaseInputType::Hunger, hunger.normalized_hunger() as f64);

        // 2: stamina (0.0 = exhausted, 1.0 = full)
        brain.set_base_input(BaseInputType::Stamina, stamina.normalized_stamina() as f64);

        // 3: time of day (normalized 0.0-1.0)
        brain.set_base_input(
            BaseInputType::TimeOfDay,
            time_of_day.normalized_time_of_day() as f64,
        );

        // 4: state novelty (0.0–1.0)
        // This represents how novel or interesting the current state is
        // For now, we'll use a simple heuristic based on time and position
        let position_hash =
            (transform.translation.x.abs() + transform.translation.z.abs()) as f64;
        let time_factor =
            (time_of_day.normalized_time_of_day() * 10.0).sin().abs() as f64;
        let novelty = ((position_hash * 0.01 + time_factor) % 1.0).abs();
        brain.set_base_input(BaseInputType::StateNovelty, novelty);

        // 5: distance to unexplored area (0.0–1.0)
        // Lower values mean unexplored areas are closer
        // For now, simulate this with a random value that changes slowly over time
        // let current_value = brain.inputs[5];
        // let change = (rng.gen::<f64>() - 0.5) * 0.1; // Small random change
        // brain.inputs[5] = (current_value + change).clamp(0.0, 1.0);
        brain.set_base_input(BaseInputType::DistanceToUnexploredArea, 0.0);

        // 6: distance to nearest predator (0.0–1.0)
        // Lower values mean predators are closer (more dangerous)
        brain.set_base_input(BaseInputType::DistanceToPredator, 0.0);
        // For now, simulate with a random value that occasionally spikes low (predator nearby)
        // if rng.gen::<f64>() < 0.01 { // 1% chance of predator encounter
        //     brain.inputs[6] = rng.gen::<f64>() * 0.3; // Close predator
        // } else {
        //     // Gradually return to safer values
        //     brain.inputs[6] = (brain.inputs[6] + 0.1).min(1.0);
        // }

        // 7: threat bearing (0.0–1.0)
        // Direction of the threat relative to the organism
        // For now, use a random value that correlates with predator distance
        // if brain.inputs[6] < 0.5 { // If predator is relatively close
        //     brain.inputs[7] = rng.gen::<f64>(); // Random direction
        // } else {
        //     brain.inputs[7] = 0.5; // No clear threat direction
        // }
        brain.set_base_input(BaseInputType::ThreatBearing, 0.0);

        // 8: cover proximity (0.0–1.0)
        // Lower values mean cover is closer/more available
        // For now, correlate somewhat with height (higher = less cover)
        // let height_factor = (transform.translation.y / 100.0).min(1.0) as f64;
        // let random_factor = rng.gen::<f64>() * 0.3;
        // brain.inputs[8] = (height_factor * 0.7 + random_factor).clamp(0.0, 1.0);
        brain.set_base_input(BaseInputType::CoverProximity, 0.0);

        // 9: local group density (0.0–1.0)
        // Higher values mean more organisms nearby
        // For now, use a random value that changes slowly
        // let current_density = brain.inputs[9];
        // let density_change = (rng.gen::<f64>() - 0.5) * 0.05;
        // brain.inputs[9] = (current_density + density_change).clamp(0.0, 1.0);
        brain.set_base_input(BaseInputType::GroupDensity, 0.0);

        // 10: resource gradient (0.0–1.0)
        // Higher values mean resources are more abundant in the direction of movement
        // For now, correlate somewhat with hunger and thirst (hungrier/thirstier = perceive more resources)
        // let need_factor = (2.0 - thirst.normalized_thirst() as f64 - hunger.normalized_hunger() as f64) / 2.0;
        // let resource_perception = need_factor * 0.7 + rng.gen::<f64>() * 0.3;
        // brain.inputs[10] = resource_perception.clamp(0.0, 1.0);
        brain.set_base_input(BaseInputType::ResourceGradient, 0.0);

        // 11: ambient visibility (0.0–1.0)
        // Higher values mean better visibility
        // Correlate with time of day (midday = best visibility)
        let time = time_of_day.normalized_time_of_day() as f64;
        let day_cycle = (time * std::f64::consts::PI * 2.0).sin() * 0.5 + 0.5; // 0-1 cycle peaking at midday
        let weather_factor = rng.gen::<f64>() * 0.2; // Random weather effect
        brain.set_base_input(
            BaseInputType::AmbientVisibility,
            (day_cycle * 0.8 + weather_factor).clamp(0.0, 1.0),
        );

        // Calculate outputs
        let brain_inputs = brain.inputs.clone();
        let outputs = brain.network.calculate(&brain_inputs);
        brain.outputs = Vec::from(outputs);
    }
}
