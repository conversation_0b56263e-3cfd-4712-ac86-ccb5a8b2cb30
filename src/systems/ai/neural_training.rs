use crate::components::ai::neural_network::{BaseOutputType, NeuralBrain};
use crate::components::lifecycle::{Hunger, Lifespan, Stamina, Thirst};
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;
use neuroflow::data::{DataSet, Extractable};
use std::collections::HashMap;

// Component to track training data
#[derive(Component)]
pub struct NeuralTrainingData {
    // Base history data for all species
    pub input_history: Vec<Vec<f64>>,
    pub output_history: Vec<Vec<f64>>,
    pub health_history: Vec<f32>,
    pub thirst_history: Vec<f32>,
    pub hunger_history: Vec<f32>,
    pub stamina_history: Vec<f32>,

    // Activity tracking for all species
    pub time_drinking: f32,
    pub time_eating: f32,
    pub time_resting: f32,
    pub time_exploring: f32,
    pub consecutive_drinking_actions: u32,
    pub consecutive_eating_actions: u32,
    pub consecutive_resting_actions: u32,
    pub consecutive_exploring_actions: u32,

    // Exploration metrics
    pub exploration_success_rate: f32,

    // Species-specific training data (stored as generic values)
    pub species_specific_metrics: HashMap<String, f32>,
    pub species_specific_counters: HashMap<String, u32>,

    // Collection timing
    pub collection_interval: f32,
    pub time_since_collection: f32,

    // Training configuration
    pub training_frequency: f32,
    pub time_since_training: f32,
}

impl Default for NeuralTrainingData {
    fn default() -> Self {
        Self {
            input_history: Vec::new(),
            output_history: Vec::new(),
            health_history: Vec::new(),
            thirst_history: Vec::new(),
            hunger_history: Vec::new(),
            stamina_history: Vec::new(),
            time_drinking: 0.0,
            time_eating: 0.0,
            time_resting: 0.0,
            time_exploring: 0.0,
            consecutive_drinking_actions: 0,
            consecutive_eating_actions: 0,
            consecutive_resting_actions: 0,
            consecutive_exploring_actions: 0,
            exploration_success_rate: 0.5, // Start with neutral success rate
            species_specific_metrics: HashMap::new(),
            species_specific_counters: HashMap::new(),
            collection_interval: 3.0,
            time_since_collection: 0.0,
            training_frequency: 30.0, // Train every 30 seconds of simulation time
            time_since_training: 0.0,
        }
    }
}

// System to collect training data
pub fn collect_training_data(
    mut query: Query<(
        &NeuralBrain,
        &Lifespan,
        &Thirst,
        &Hunger,
        &Stamina,
        &mut NeuralTrainingData,
    )>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (brain, lifespan, thirst, hunger, stamina, mut training_data) in query.iter_mut()
    {
        training_data.time_since_collection += delta_time.0;
        training_data.time_since_training += delta_time.0;

        // Track base activities using the new accessor methods
        let is_drinking =
            brain.get_base_output(BaseOutputType::Drink).unwrap_or(0.0) > 0.6;
        let is_eating = brain.get_base_output(BaseOutputType::Eat).unwrap_or(0.0) > 0.6;
        let is_resting = brain.get_base_output(BaseOutputType::Rest).unwrap_or(0.0) > 0.6;
        let is_exploring = brain
            .get_base_output(BaseOutputType::Explore)
            .unwrap_or(0.0)
            > 0.6;

        // Update activity tracking for base behaviors
        if is_drinking {
            training_data.time_drinking += delta_time.0;
            training_data.consecutive_drinking_actions += 1;

            // Reset other activity counters when switching activities
            training_data.consecutive_eating_actions = 0;
            training_data.consecutive_resting_actions = 0;
            training_data.consecutive_exploring_actions = 0;
        } else {
            training_data.consecutive_drinking_actions = 0;
        }

        if is_eating {
            training_data.time_eating += delta_time.0;
            training_data.consecutive_eating_actions += 1;

            // Reset other activity counters
            training_data.consecutive_drinking_actions = 0;
            training_data.consecutive_resting_actions = 0;
            training_data.consecutive_exploring_actions = 0;
        } else {
            training_data.consecutive_eating_actions = 0;
        }

        if is_resting {
            training_data.time_resting += delta_time.0;
            training_data.consecutive_resting_actions += 1;

            // Reset other activity counters
            training_data.consecutive_drinking_actions = 0;
            training_data.consecutive_eating_actions = 0;
            training_data.consecutive_exploring_actions = 0;
        } else {
            training_data.consecutive_resting_actions = 0;
        }

        if is_exploring {
            training_data.time_exploring += delta_time.0;
            training_data.consecutive_exploring_actions += 1;

            // Reset other activity counters
            training_data.consecutive_drinking_actions = 0;
            training_data.consecutive_eating_actions = 0;
            training_data.consecutive_resting_actions = 0;

            // Occasionally update exploration success rate
            if rand::random::<f32>() < 0.05 {
                let success_change = if lifespan.health > 0.7
                    && thirst.normalized_thirst() > 0.6
                    && hunger.normalized_hunger() > 0.6
                {
                    rand::random::<f32>() * 0.1 // Small positive change
                } else {
                    -rand::random::<f32>() * 0.1 // Small negative change
                };

                training_data.exploration_success_rate += success_change;
                training_data.exploration_success_rate =
                    training_data.exploration_success_rate.clamp(0.0, 1.0);
            }
        } else {
            training_data.consecutive_exploring_actions = 0;
        }

        // Collect data at regular intervals
        if training_data.time_since_collection >= training_data.collection_interval {
            training_data.time_since_collection = 0.0;

            // Record current inputs, outputs, and lifecycle states
            training_data.input_history.push(brain.inputs.clone());
            training_data.output_history.push(brain.outputs.clone());
            training_data.health_history.push(lifespan.health);
            training_data
                .thirst_history
                .push(thirst.normalized_thirst());
            training_data
                .hunger_history
                .push(hunger.normalized_hunger());
            training_data
                .stamina_history
                .push(stamina.normalized_stamina());

            // Limit history size
            if training_data.input_history.len() > 100 {
                training_data.input_history.remove(0);
                training_data.output_history.remove(0);
                training_data.health_history.remove(0);
                training_data.thirst_history.remove(0);
                training_data.hunger_history.remove(0);
                training_data.stamina_history.remove(0);
            }
        }
    }
}

// System to train neural networks periodically
pub fn train_neural_networks(mut query: Query<(&mut NeuralBrain, &NeuralTrainingData)>) {
    for (mut brain, training_data) in query.iter_mut() {
        // Only train if we have enough data
        if training_data.input_history.len() < 10 {
            continue;
        }

        let mut data_set = DataSet::new();

        // Calculate rewards based on changes in health and lifecycle components
        for i in 1..training_data.health_history.len() {
            // Calculate changes in various metrics
            let health_change =
                training_data.health_history[i] - training_data.health_history[i - 1];
            let thirst_change =
                training_data.thirst_history[i] - training_data.thirst_history[i - 1];
            let hunger_change =
                training_data.hunger_history[i] - training_data.hunger_history[i - 1];
            let stamina_change =
                training_data.stamina_history[i] - training_data.stamina_history[i - 1];

            // Weight health more heavily as it's the ultimate indicator of organism well-being
            let weighted_change =
                (health_change * 2.0) + thirst_change + hunger_change + stamina_change;

            // Determine reward factor based on weighted changes
            let max_wc = 2.0 + 1.0 + 1.0 + 1.0;
            let reward_factor = (weighted_change / max_wc).clamp(-1.0, 1.0);

            // Scale reward by magnitude of change
            let reward_magnitude = weighted_change.abs().min(0.5) * 0.2;

            // Adjust outputs based on outcomes
            let mut target_outputs = training_data.output_history[i - 1].clone();

            // Get the current thirst and hunger levels to determine if drinking/eating is necessary
            let current_thirst_level = training_data.thirst_history[i];
            let current_hunger_level = training_data.hunger_history[i];
            let current_stamina_level = training_data.stamina_history[i];

            // Define thresholds for when drinking/eating is necessary vs. excessive
            const HIGH_THIRST_THRESHOLD: f32 = 0.8; // Above 80% hydration is high
            const HIGH_HUNGER_THRESHOLD: f32 = 0.8; // Above 80% satiation is high
            const HIGH_STAMINA_THRESHOLD: f32 = 0.9; // Above 90% stamina is high
            const LOW_THIRST_THRESHOLD: f32 = 0.3; // Below 30% hydration is low
            const LOW_HUNGER_THRESHOLD: f32 = 0.3; // Below 30% satiation is low
            const LOW_STAMINA_THRESHOLD: f32 = 0.3; // Below 30% stamina is low

            // Apply adjustments to base outputs
            if let Some(&drink_idx) =
                brain.output_config.base_mapping.get(&BaseOutputType::Drink)
            {
                // Penalties for excessive drinking
                let excessive_drinking_penalty = if current_thirst_level
                    > HIGH_THIRST_THRESHOLD
                    && training_data.consecutive_drinking_actions > 3
                {
                    // Stronger penalty for longer excessive drinking
                    let base_penalty = -0.2;
                    let duration_factor =
                        (training_data.consecutive_drinking_actions as f32 - 3.0) * 0.05;
                    (base_penalty - duration_factor.min(0.3)) as f64
                } else {
                    0.0
                };

                // Apply the base reward adjustment
                let mut adjustment = (reward_factor * reward_magnitude * 0.1) as f64;
                adjustment += excessive_drinking_penalty;

                // Encourage drinking when thirst is low
                if current_thirst_level < LOW_THIRST_THRESHOLD {
                    adjustment += 0.2;
                }

                target_outputs[drink_idx] += adjustment;
                target_outputs[drink_idx] = target_outputs[drink_idx].clamp(0.0, 1.0);
            }

            if let Some(&eat_idx) =
                brain.output_config.base_mapping.get(&BaseOutputType::Eat)
            {
                // Penalties for excessive eating
                let excessive_eating_penalty = if current_hunger_level
                    > HIGH_HUNGER_THRESHOLD
                    && training_data.consecutive_eating_actions > 3
                {
                    // Stronger penalty for longer excessive eating
                    let base_penalty = -0.2;
                    let duration_factor =
                        (training_data.consecutive_eating_actions as f32 - 3.0) * 0.05;
                    (base_penalty - duration_factor.min(0.3)) as f64
                } else {
                    0.0
                };

                // Apply the base reward adjustment
                let mut adjustment = (reward_factor * reward_magnitude * 0.1) as f64;
                adjustment += excessive_eating_penalty;

                // Encourage eating when hunger is low
                if current_hunger_level < LOW_HUNGER_THRESHOLD {
                    adjustment += 0.2;
                }

                target_outputs[eat_idx] += adjustment;
                target_outputs[eat_idx] = target_outputs[eat_idx].clamp(0.0, 1.0);
            }

            if let Some(&rest_idx) =
                brain.output_config.base_mapping.get(&BaseOutputType::Rest)
            {
                // Penalty for excessive resting
                let excessive_resting_penalty = if current_stamina_level
                    > HIGH_STAMINA_THRESHOLD
                    && training_data.consecutive_resting_actions > 3
                {
                    // Stronger penalty for longer excessive resting
                    let base_penalty = -0.2;
                    let duration_factor =
                        (training_data.consecutive_resting_actions as f32 - 3.0) * 0.05;
                    (base_penalty - duration_factor.min(0.3)) as f64
                } else {
                    0.0
                };

                // Apply the base reward adjustment
                let mut adjustment = (reward_factor * reward_magnitude * 0.1) as f64;
                adjustment += excessive_resting_penalty;

                // Encourage resting when stamina is low
                if current_stamina_level < LOW_STAMINA_THRESHOLD {
                    adjustment += 0.2;
                }

                target_outputs[rest_idx] += adjustment;
                target_outputs[rest_idx] = target_outputs[rest_idx].clamp(0.0, 1.0);
            }

            if let Some(&explore_idx) = brain
                .output_config
                .base_mapping
                .get(&BaseOutputType::Explore)
            {
                // Penalty for excessive exploring when needs are not met
                let excessive_exploring_penalty = if (current_thirst_level
                    < LOW_THIRST_THRESHOLD
                    || current_hunger_level < LOW_HUNGER_THRESHOLD
                    || current_stamina_level < LOW_STAMINA_THRESHOLD)
                    && training_data.consecutive_exploring_actions > 2
                {
                    // Stronger penalty for exploring when basic needs are not met
                    let base_penalty = -0.3;
                    let duration_factor =
                        (training_data.consecutive_exploring_actions as f32 - 2.0) * 0.1;
                    (base_penalty - duration_factor.min(0.4)) as f64
                } else {
                    0.0
                };

                // Bonus for successful exploration when needs are met
                let exploration_bonus = if current_thirst_level > HIGH_THIRST_THRESHOLD
                    && current_hunger_level > HIGH_HUNGER_THRESHOLD
                    && current_stamina_level > HIGH_STAMINA_THRESHOLD
                    && training_data.exploration_success_rate > 0.6
                {
                    // Reward successful exploration when organism is in good condition
                    (0.1 + (training_data.exploration_success_rate - 0.6) * 0.2) as f64
                } else {
                    0.0
                };

                // Apply the base reward adjustment
                let mut adjustment = (reward_factor * reward_magnitude * 0.1) as f64;
                adjustment += excessive_exploring_penalty;
                adjustment += exploration_bonus;

                // Encourage exploration when all needs are satisfied
                if current_thirst_level > HIGH_THIRST_THRESHOLD
                    && current_hunger_level > HIGH_HUNGER_THRESHOLD
                    && current_stamina_level > HIGH_STAMINA_THRESHOLD
                {
                    adjustment += 0.15;
                }

                target_outputs[explore_idx] += adjustment;
                target_outputs[explore_idx] = target_outputs[explore_idx].clamp(0.0, 1.0);
            }

            // Push the adjusted target outputs to the network's training data
            data_set.push(&training_data.input_history[i - 1], &target_outputs);
        }

        // Skip training if dataset is empty
        if data_set.len() == 0 {
            continue;
        }

        // Train the network
        brain.network.train(&data_set, 1_000);
    }
}
