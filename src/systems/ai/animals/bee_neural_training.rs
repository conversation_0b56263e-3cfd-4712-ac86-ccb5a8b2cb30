use crate::components::ai::neural_network::{
    NeuralBrain, SpeciesType
};
use crate::components::animals::bees::{Bee, BeeHive, HomeBeeHive};
use crate::components::plants::{AnimalPollinatedPlant, NectarProduction};
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::ai::neural_training::NeuralTrainingData;
use bevy::prelude::{Entity, GlobalTransform, Query, Res, Transform, With};
use neuroflow::data::{DataSet, Extractable};

// System to collect bee-specific training data
pub fn collect_bee_training_data(
    mut query: Query<
        (&NeuralBrain, &mut NeuralTrainingData, &Bee, Option<&HomeBeeHive>),
        With<Bee>,
    >,
    bee_hives: Query<&BeeHive>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (brain, mut training_data, bee, home_hive) in query.iter_mut() {
        // Skip if not a bee brain
        if brain.species_type != SpeciesType::Bee {
            continue;
        }

        // Track bee-specific behaviors
        let is_pollinating = brain.get_extended_output("Pollinate").unwrap_or(0.0) > 0.6;
        let is_dancing = brain.get_extended_output("Dance").unwrap_or(0.0) > 0.6;
        let is_returning = brain.get_extended_output("ReturnToHive").unwrap_or(0.0) > 0.6;

        // Update bee-specific metrics
        if is_pollinating {
            *training_data
                .species_specific_metrics
                .entry("time_pollinating".to_string())
                .or_insert(0.0) += delta_time.0;
            *training_data
                .species_specific_counters
                .entry("consecutive_pollinating".to_string())
                .or_insert(0) += 1;

            // Reset other bee-specific counters
            training_data
                .species_specific_counters
                .insert("consecutive_dancing".to_string(), 0);
            training_data
                .species_specific_counters
                .insert("consecutive_returning".to_string(), 0);
        } else {
            training_data
                .species_specific_counters
                .insert("consecutive_pollinating".to_string(), 0);
        }

        if is_dancing {
            *training_data
                .species_specific_metrics
                .entry("time_dancing".to_string())
                .or_insert(0.0) += delta_time.0;
            *training_data
                .species_specific_counters
                .entry("consecutive_dancing".to_string())
                .or_insert(0) += 1;

            // Reset other bee-specific counters
            training_data
                .species_specific_counters
                .insert("consecutive_pollinating".to_string(), 0);
            training_data
                .species_specific_counters
                .insert("consecutive_returning".to_string(), 0);
        } else {
            training_data
                .species_specific_counters
                .insert("consecutive_dancing".to_string(), 0);
        }

        if is_returning {
            *training_data
                .species_specific_metrics
                .entry("time_returning".to_string())
                .or_insert(0.0) += delta_time.0;
            *training_data
                .species_specific_counters
                .entry("consecutive_returning".to_string())
                .or_insert(0) += 1;

            // Reset other bee-specific counters
            training_data
                .species_specific_counters
                .insert("consecutive_pollinating".to_string(), 0);
            training_data
                .species_specific_counters
                .insert("consecutive_dancing".to_string(), 0);
        } else {
            training_data
                .species_specific_counters
                .insert("consecutive_returning".to_string(), 0);
        }

        // Track nectar collection success
        if is_pollinating && bee.nectar_carried < bee.max_nectar {
            *training_data
                .species_specific_metrics
                .entry("nectar_collection_rate".to_string())
                .or_insert(0.0) += (bee.nectar_carried / bee.max_nectar) * delta_time.0;
        }

        // Track hive return success
        if let Some(home_hive_ref) = home_hive {
            if is_returning && bee.nectar_carried > 0.0 {
                if let Ok(hive) = bee_hives.get(home_hive_ref.0) {
                    *training_data
                        .species_specific_metrics
                        .entry("hive_return_success".to_string())
                        .or_insert(0.0) +=
                        (hive.nectar_stored / hive.max_nectar) * delta_time.0;
                }
            }
        }

        // Track pollen load
        if let Some(pollen_load) = brain.get_extended_input("PollenLoad") {
            *training_data
                .species_specific_metrics
                .entry("avg_pollen_load".to_string())
                .or_insert(0.0) = (pollen_load as f32 * 0.1)
                + (*training_data
                    .species_specific_metrics
                    .get("avg_pollen_load")
                    .unwrap_or(&0.0)
                    * 0.9);
        }
    }
}

// System to train bee-specific neural networks
pub fn train_bee_neural_networks(
    mut query: Query<(&mut NeuralBrain, &NeuralTrainingData, &Bee)>,
) {
    for (mut brain, training_data, bee) in query.iter_mut() {
        // Skip if not a bee brain or not enough data
        if brain.species_type != SpeciesType::Bee
            || training_data.input_history.len() < 10
        {
            continue;
        }

        // Get indices for bee-specific outputs
        let pollinate_idx = match brain.output_config.extended_mapping.get("Pollinate") {
            Some(&idx) => idx,
            None => continue, // Skip if bee doesn't have this output
        };

        let dance_idx = match brain.output_config.extended_mapping.get("Dance") {
            Some(&idx) => idx,
            None => continue,
        };

        let return_idx = match brain.output_config.extended_mapping.get("ReturnToHive") {
            Some(&idx) => idx,
            None => continue,
        };

        // Get indices for bee-specific inputs
        let flower_idx = match brain.input_config.extended_mapping.get("FlowerProximity")
        {
            Some(&idx) => idx,
            None => continue,
        };

        let nectar_idx = match brain.input_config.extended_mapping.get("NectarQuality") {
            Some(&idx) => idx,
            None => continue,
        };

        let pollen_idx = match brain.input_config.extended_mapping.get("PollenLoad") {
            Some(&idx) => idx,
            None => continue,
        };

        let mut data: DataSet = DataSet::new();

        // Process training data
        for i in 1..training_data.input_history.len() {
            let mut target_outputs = training_data.output_history[i - 1].clone();

            // Get current state
            let current_inputs = &training_data.input_history[i];
            let flower_proximity = current_inputs[flower_idx];
            let nectar_quality = current_inputs[nectar_idx];
            let pollen_load = current_inputs[pollen_idx];

            // Get health metrics
            let health = training_data.health_history[i];
            let thirst = training_data.thirst_history[i];
            let hunger = training_data.hunger_history[i];

            // Calculate bee-specific rewards

            // 1. Pollinate behavior adjustments
            let mut pollinate_adjustment = 0.0;

            // Encourage pollination when near flowers with low pollen load
            if flower_proximity > 0.4 && pollen_load < 0.6 {
                pollinate_adjustment += 0.2;
            }

            // Encourage pollination when health is high
            if health > 0.8 {
                pollinate_adjustment += 0.1;
            }

            // Encourage pollination when nectar quality is high
            if nectar_quality > 0.7 {
                pollinate_adjustment += 0.1;
            }

            // Encourage pollination when pollen load is low
            if pollen_load < 0.3 {
                pollinate_adjustment += 0.1;
            }

            // Discourage pollination when already full of pollen
            if pollen_load > 0.8 {
                pollinate_adjustment -= 0.3;
            }

            // Discourage pollination when health metrics are critical
            if thirst < 0.3 || hunger < 0.3 {
                pollinate_adjustment -= 0.4;
            }

            // 2. Dance behavior adjustments
            let mut dance_adjustment = 0.0;

            // Encourage dancing when nectar quality is high and bee has returned to hive
            if nectar_quality > 0.7 && pollen_load > 0.5 {
                dance_adjustment += 0.3;
            } else {
                dance_adjustment -= 0.2;
            }

            // 3. Return to hive behavior adjustments
            let mut return_adjustment = 0.0;

            // Encourage returning when pollen load is high
            if pollen_load > 0.7 {
                return_adjustment += 0.4;
            }

            // Encourage returning when health is low
            if health < 0.4 || thirst < 0.3 || hunger < 0.3 {
                return_adjustment += 0.3;
            }

            // Penalize returning when pollen load is low
            if pollen_load < 0.4 {
                return_adjustment -= 0.5;
            }

            // Apply all adjustments
            target_outputs[pollinate_idx] += pollinate_adjustment;
            target_outputs[pollinate_idx] = target_outputs[pollinate_idx].clamp(0.0, 1.0);

            target_outputs[dance_idx] += dance_adjustment;
            target_outputs[dance_idx] = target_outputs[dance_idx].clamp(0.0, 1.0);

            target_outputs[return_idx] += return_adjustment;
            target_outputs[return_idx] = target_outputs[return_idx].clamp(0.0, 1.0);

            // Push the adjusted target outputs to the network's training data
            data.push(&training_data.input_history[i - 1], &target_outputs);
        }

        // Skip training if dataset is empty
        if data.len() == 0 {
            continue;
        }

        // Train the network
        brain.network.train(&data, 500);

        // Save the trained network periodically
        // if rand::random::<f32>() < 0.01 {
        //     // 1% chance each update
        //     let path = format!("{NEURAL_NETWORK_PATH}/bee.flow");
        //     brain.network.save_nn(&path);
        //
        //     if rand::random::<f32>() < 0.01 {
        //         log::info!("Saved neural network to {}", path);
        //     }
        // }
    }
}

// System to update bee-specific neural inputs
pub fn update_bee_neural_inputs(
    mut query: Query<(&mut NeuralBrain, &Bee, &Transform, Option<&HomeBeeHive>)>,
    plants_query: Query<
        (&GlobalTransform, &NectarProduction),
        With<AnimalPollinatedPlant>,
    >,
    bee_hives_query: Query<(Entity, &GlobalTransform, &BeeHive)>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut brain, bee, transform, home_hive) in query.iter_mut() {
        // Skip if not a bee brain or not time to update
        if brain.species_type != SpeciesType::Bee {
            continue;
        }

        brain.last_update += delta_time.0;
        if brain.last_update < brain.update_frequency {
            continue;
        }

        // Reset update timer
        brain.last_update = 0.0;

        // Update bee-specific inputs

        // 1. Find nearest flower and calculate proximity
        let mut closest_flower_distance = f32::MAX;
        let mut best_nectar_quality = 0.0;

        for (plant_transform, nectar_production) in plants_query.iter() {
            let distance = transform
                .translation
                .distance(plant_transform.translation());

            if distance < closest_flower_distance {
                closest_flower_distance = distance;

                // Calculate nectar quality based on production rate and amount
                best_nectar_quality =
                    nectar_production.current_nectar / nectar_production.max_nectar;
            }
        }

        // Convert distance to proximity (0.0 = far, 1.0 = close)
        let max_detection_range = 20.0;
        let flower_proximity = if closest_flower_distance < max_detection_range {
            1.0 - (closest_flower_distance / max_detection_range)
        } else {
            0.0
        };

        // 2. Calculate distance to home hive
        let mut hive_proximity = 0.0;

        if let Some(home_hive_ref) = home_hive {
            for (hive_entity, hive_transform, _) in bee_hives_query.iter() {
                if hive_entity == home_hive_ref.0 {
                    let distance =
                        transform.translation.distance(hive_transform.translation());
                    let max_hive_range = 30.0;

                    hive_proximity = if distance < max_hive_range {
                        1.0 - (distance / max_hive_range)
                    } else {
                        0.0
                    };

                    break;
                }
            }
        }

        // 3. Set bee-specific inputs
        brain.set_extended_input("FlowerProximity", flower_proximity as f64);
        brain.set_extended_input("NectarQuality", best_nectar_quality as f64);
        brain.set_extended_input(
            "PollenLoad",
            (bee.nectar_carried / bee.max_nectar) as f64,
        );

        // Calculate outputs based on updated inputs
        brain.calculate_outputs();
    }
}
