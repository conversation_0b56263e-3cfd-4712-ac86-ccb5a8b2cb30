use avian3d::prelude::LinearVelocity;
use bevy::prelude::*;
use bevy_gltf_animation::prelude::GltfAnimations;
use rand::Rng;

use crate::behaviors::common::EatFoodBehavior;
use crate::components::animals::animation_state::{
    AnimationConfig, AnimationState, OrganismAnimation
};
use crate::components::animals::InWater;
use crate::components::environment::{Terrain, TerrainPod, TerrainType};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};
use crate::resources::WorldSimulationDeltaTime;

/// Generic animation system that works for all organisms
pub fn generic_animation_system(
    mut commands: Commands,
    mut organism_query: Query<(
        Entity,
        &mut AnimationState,
        &mut AnimationConfig,
        &mut GltfAnimations,
        &LinearVelocity,
        Option<&Dying>,
        Option<&EatFoodBehavior>,
        Option<&CurrentTerrainPod>,
        Option<&InWater>,
    )>,
    mut animation_players: Query<&mut AnimationPlayer>,
    terrain_query: Query<&Terrain>,
    terrain_pod_query: Query<&TerrainPod>,
    delta_time: Option<Res<WorldSimulationDeltaTime>>,
) {
    let dt = delta_time.map(|d| d.0).unwrap_or(1.0 / 60.0); // Default to 60 FPS if no delta time
    for (
        entity,
        mut animation_state,
        mut animation_config,
        mut gltf_animations,
        linear_velocity,
        dying,
        eating_behavior,
        current_pod,
        in_water,
    ) in organism_query.iter_mut()
    {
        // Update timers
        animation_state.update_time(dt);
        animation_config.update_idle_timer(dt);

        // Determine the appropriate animation based on organism state
        let desired_animation = determine_animation(
            &linear_velocity,
            &animation_config,
            dying,
            eating_behavior,
            current_pod,
            &terrain_query,
            &terrain_pod_query,
            in_water,
        );

        // Calculate animation speed based on velocity
        let animation_speed = calculate_animation_speed(
            &desired_animation,
            &linear_velocity,
            &animation_config,
        );

        // Check if we need to change animation
        if should_change_animation(&animation_state, &desired_animation) {
            animation_state.transition_to(desired_animation.clone());
            animation_state.should_loop = desired_animation.should_loop();
            animation_state.speed_multiplier = animation_speed;
        } else {
            // Update speed for current animation
            animation_state.speed_multiplier = animation_speed;
        }

        // Handle idle animation variations
        if matches!(animation_state.current_animation, OrganismAnimation::Idle)
            && animation_config.should_change_idle_animation()
        {
            let idle_variations = [
                OrganismAnimation::Idle,
                OrganismAnimation::Sit,
                OrganismAnimation::Lay,
            ];
            let random_idle = idle_variations
                [rand::thread_rng().gen_range(0..idle_variations.len())]
            .clone();
            animation_state.transition_to(random_idle);
        }

        // Apply the animation to the animation player
        apply_animation_to_player(
            entity,
            &animation_state,
            &mut gltf_animations,
            &mut animation_players,
            &mut commands,
        );
    }
}

/// Determine what animation should be playing based on organism state
fn determine_animation(
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
    dying: Option<&Dying>,
    eating_behavior: Option<&EatFoodBehavior>,
    current_pod: Option<&CurrentTerrainPod>,
    terrain_query: &Query<&Terrain>,
    terrain_pod_query: &Query<&TerrainPod>,
    in_water: Option<&InWater>,
) -> OrganismAnimation {
    // Death has the highest priority
    if dying.is_some() {
        return OrganismAnimation::Death;
    }

    // Eating has the second-highest priority
    if eating_behavior.is_some() {
        return OrganismAnimation::Eat;
    }

    // Check if organism is in water (swimming)
    if in_water.is_some() {
        return OrganismAnimation::Swim;
    }

    // Movement-based animations
    let speed = linear_velocity.0.length();

    if speed < animation_config.idle_threshold {
        OrganismAnimation::Idle
    } else if speed >= animation_config.run_threshold {
        OrganismAnimation::Run
    } else {
        OrganismAnimation::Walk
    }
}

/// Calculate the appropriate animation speed based on velocity and animation type
fn calculate_animation_speed(
    animation: &OrganismAnimation,
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
) -> f32 {
    let speed = linear_velocity.0.length();

    match animation {
        OrganismAnimation::Walk => {
            // Scale walk animation speed based on actual velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            animation_config.walk_speed_multiplier * (0.5 + normalized_speed * 0.5)
        }
        OrganismAnimation::Run => {
            // Scale run animation speed based on velocity above run threshold
            let excess_speed = (speed - animation_config.run_threshold).max(0.0);
            animation_config.run_speed_multiplier * (1.0 + excess_speed * 0.1)
        }
        OrganismAnimation::Swim => {
            // Swimming speed based on velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            0.8 + normalized_speed * 0.4
        }
        _ => 1.0, // Default speed for other animations
    }
}

/// Check if the animation should be changed
fn should_change_animation(
    current_state: &AnimationState,
    desired_animation: &OrganismAnimation,
) -> bool {
    // Always change if the desired animation has higher priority
    if desired_animation.get_priority() > current_state.current_animation.get_priority() {
        return true;
    }

    // Change if it's a different animation of the same or lower priority
    if desired_animation != &current_state.current_animation {
        return true;
    }

    false
}

/// Apply the animation state to the actual animation player
fn apply_animation_to_player(
    entity: Entity,
    animation_state: &AnimationState,
    gltf_animations: &mut GltfAnimations,
    animation_players: &mut Query<&mut AnimationPlayer>,
    _commands: &mut Commands,
) {
    let Ok(mut player) = animation_players.get_mut(gltf_animations.animation_player)
    else {
        warn!("No animation player found for entity {:?}", entity);
        return;
    };

    let animation_index = animation_state.current_animation.get_animation_index();

    let Some(animation_handle) = gltf_animations.get(animation_index) else {
        warn!("Animation index {} not found for entity {:?}", animation_index, entity);
        return;
    };

    // Check if we need to start a new animation
    if !player.is_playing_animation(animation_handle) {
        let mut animation = player.start(animation_handle);

        // Configure the animation
        animation.set_speed(animation_state.speed_multiplier);

        if animation_state.should_loop {
            animation.repeat();
        }
    } else {
        // Update the speed of the currently playing animation
        if let Some(mut active_animation) = player.animation_mut(animation_handle) {
            active_animation.set_speed(animation_state.speed_multiplier);
        }
    }
}

/// System to initialize animation components for organisms that don't have them
pub fn initialize_animation_components(
    mut commands: Commands,
    query: Query<Entity, (With<GltfAnimations>, Without<AnimationState>)>,
) {
    for entity in query.iter() {
        commands
            .entity(entity)
            .insert((AnimationState::default(), AnimationConfig::default()));
    }
}
