use crate::behaviors::{create_neural_needs_behavior_tree, PlantTarget};
use crate::components::ai::neural_network::NeuralBrain;
use crate::components::animals::bees::{Bee, BeeHive, HomeBeeHive, TreeHasBeeHive};
use crate::components::animals::{
    AutoRigColliders, LastVisitedPlants, MovementDampingFactor
};
use crate::components::ecosystem::FoodType;
use crate::components::lifecycle::{
    BiologicalGender, BiologicalOrganism, Gender, Hunger, Stamina, Thirst
};
use crate::components::perception::{
    AIPerception, SenseType, SightPerception, SmellPerception, StimulusData, StimulusSource, StimulusSources, StimulusType
};
use crate::components::plants::PlantTree;
use crate::components::MeshSetupComplete;
use crate::post_process::pixelated_material::{
    ExtendedPixelatedMaterial, PixelatedMaterial
};
use crate::resources::animals::AnimalsAssetsKeys;
use crate::resources::{TimeOfDay, WorldSimulationDeltaTime};
use crate::systems::ai::neural_training::NeuralTrainingData;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::debug_render::DebugRender;
use avian3d::prelude::{
    AngularVelocity, Collider, ColliderConstructor, ColliderConstructorHierarchy, GravityScale, LinearVelocity, Mass, MaxAngularSpeed, MaxLinearSpeed, RigidBody, TransformInterpolation
};
use bevy::gltf::GltfMaterialName;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_behave::prelude::*;
use bevy_descendant_collector::DescendantCollectorTarget;
use bevy_tween::combinator::{sequence, tween};
use bevy_tween::interpolate::{angle_z_to, AngleZ, Scale};
use bevy_tween::prelude::*;
use bevy_tween::tween::{AnimationTarget, TargetComponent};
use rand::prelude::SliceRandom;
use rand::{random, Rng};

const POSSIBLE_GENDERS: [BiologicalGender; 2] =
    [BiologicalGender::Female, BiologicalGender::Male];

pub fn spawn_bee_hive(
    mut commands: Commands,
    trees: Query<Entity, (Without<TreeHasBeeHive>, With<PlantTree>)>,
    children_query: Query<&Children>,
    material_query: Query<(&GltfMaterialName, &Aabb), With<Mesh3d>>,
    animals_assets: AnimalsAssetSystemParams,
    mut spawned: Local<bool>,
) {
    if *spawned {
        return;
    }

    // Get first tree
    let Some(tree_entity) = trees.iter().next() else {
        return;
    };

    // Get tree height based on model (nested child)
    let mut tree_height = 0.0;
    for child in children_query.iter_descendants(tree_entity) {
        if let Ok((material_name, aabb)) = material_query.get(child) {
            if material_name.0.to_lowercase().contains("bark") {
                tree_height = aabb.half_extents.y;
                break;
            }
        }
    }

    if tree_height == 0.0 {
        return;
    }

    let Some(bee_hive_model) = animals_assets.get_model(AnimalsAssetsKeys::BEE_HIVE)
    else {
        return;
    };

    tree_height += rand::thread_rng().gen_range(0.0..0.5);
    let target_pos = Vec3::new(0.0, tree_height / 4.0, 0.0);

    // Create stimulus sources for the bee hive
    let mut stimulus_sources = StimulusSources::new();

    let data = StimulusData::Food {
        nutrition_value: 3.0,
        food_type: FoodType::Sugar,
        is_consumable: true,
    };

    // Smell stimulus for the bee hive
    stimulus_sources
        .add(StimulusSource::new(StimulusType::Smell, 1.0).with_data(data.clone()));

    // Visual stimulus for the bee hive
    stimulus_sources
        .add(StimulusSource::new(StimulusType::Visual, 0.7).with_data(data.clone()));

    // Create a spawn builder with common components
    let mut spawn_builder = commands.spawn((
        Name::new("Bee Hive"),
        bee_hive_model,
        BeeHive::default(),
        stimulus_sources,
        ColliderConstructorHierarchy::new(ColliderConstructor::ConvexHullFromMesh),
        RigidBody::Static,
        Transform::from_translation(target_pos).with_scale(Vec3::splat(0.3)),
    ));

    let hive_entity = spawn_builder.id();

    commands.entity(hive_entity).insert(ChildOf(tree_entity));
    commands.entity(tree_entity).insert(TreeHasBeeHive);
    *spawned = true;
}

pub fn setup_bee_animation_wings(
    mut commands: Commands,
    bees: Query<Entity, (With<Bee>, Without<MeshSetupComplete>)>,
    children_query: Query<&Children>,
    query: Query<(&Name, &Transform), Without<AnimationTarget>>,
    mut tracked_completed: Local<HashMap<Entity, i32>>,
) {
    for bee_entity in bees.iter() {
        for child in children_query.iter_descendants(bee_entity) {
            if let Ok((name, transform)) = query.get(child) {
                let name = name.to_lowercase();

                if name.contains("anchor") {
                    continue;
                }

                if name.contains("hindwing_") || name.contains("forewing_") {
                    let mut tracked_count = None;

                    // Increase count for every wing found. After all 4 wings are found, mark the bee as complete
                    if let Some(current_count) = tracked_completed.get(&bee_entity) {
                        tracked_count = Some(current_count + 1);
                    } else {
                        tracked_count = Some(1);
                    }

                    if let Some(count) = tracked_count {
                        tracked_completed.insert(bee_entity, count);

                        if count == 4 {
                            tracked_completed.remove(&bee_entity);
                            commands.entity(bee_entity).insert(MeshSetupComplete);
                        }
                    }

                    let initial_rotation = transform.rotation;
                    let init_rot_z = initial_rotation.z;

                    let modifier = 30.0_f32.to_radians();
                    let start_angle = init_rot_z - modifier;
                    let end_angle = init_rot_z + modifier;

                    let tween_forward = ComponentTween::new_target(
                        TargetComponent::marker(),
                        AngleZ {
                            start: start_angle,
                            end: end_angle,
                        },
                    );

                    let tween_backward = ComponentTween::new_target(
                        TargetComponent::marker(),
                        AngleZ {
                            start: end_angle,
                            end: start_angle,
                        },
                    );

                    let duration = 0.01 + random::<f32>() * 0.05;

                    commands
                        .entity(child)
                        .insert(AnimationTarget)
                        .animation()
                        .repeat(Repeat::Infinitely)
                        .insert(sequence((
                            tween(
                                Duration::from_secs_f32(duration),
                                EaseKind::ElasticInOut,
                                tween_forward,
                            ),
                            tween(
                                Duration::from_secs_f32(duration),
                                EaseKind::ElasticInOut,
                                tween_backward,
                            ),
                        )));
                }
            }
        }
    }
}

pub fn handle_bee_materials(
    mut commands: Commands,
    standard_materials: Res<Assets<StandardMaterial>>,
    mut materials: ResMut<Assets<ExtendedPixelatedMaterial>>,
    bees: Query<Entity, With<Bee>>,
    children_query: Query<&Children>,
    query: Query<
        (&Name, &MeshMaterial3d<StandardMaterial>, &GltfMaterialName),
        Added<MeshMaterial3d<StandardMaterial>>,
    >,
) {
    for bee_entity in bees.iter() {
        for child in children_query.iter_descendants(bee_entity) {
            let Ok((name, material_handle, material_name)) = query.get(child) else {
                continue;
            };

            let Some(material) = standard_materials.get(material_handle) else {
                continue;
            };

            let mut new_material = material.clone();

            if name.to_lowercase().contains("wing") {
                new_material.alpha_mode = AlphaMode::Add;
                new_material.base_color = Color::WHITE;
            }

            commands
                .entity(child)
                .remove::<MeshMaterial3d<StandardMaterial>>()
                .insert(MeshMaterial3d(
                    materials.add(PixelatedMaterial::new(new_material, 4)),
                ));
        }
    }
}

// Clean up last visited plants
pub fn clean_up_last_visited_plants(
    mut query: Query<&mut LastVisitedPlants>,
    time_of_day: Res<TimeOfDay>,
) {
    for mut last_visited in query.iter_mut() {
        last_visited.0.retain(|time_visited| {
            time_of_day.elapsed_seconds - time_visited.visited_time < 30.0
        });
    }
}

// Bee loses nectar over time
pub fn lose_nectar_system(
    time: Res<WorldSimulationDeltaTime>,
    mut bee_query: Query<&mut Bee, Without<PlantTarget>>,
) {
    for mut bee in bee_query.iter_mut() {
        bee.nectar_carried -= 0.0001 * time.0;
        bee.nectar_carried = bee.nectar_carried.max(0.0);
    }
}

// --- Bee Hive Systems ---
pub fn produce_honey_for_bee_hive(
    mut query: Query<&mut BeeHive>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for mut hive in query.iter_mut() {
        if hive.nectar_stored > 0.0 {
            hive.is_producing = true;
            let nectar_to_convert = delta_time.0 * hive.honey_production_rate;
            let nectar_used = nectar_to_convert.min(hive.nectar_stored);

            hive.nectar_stored -= nectar_used;
            hive.current_honey += nectar_used;
            hive.current_honey = hive.current_honey.min(hive.max_honey);
        } else {
            hive.is_producing = false;
        }
    }
}

// Spawn bees if they have enough honey and not enough workers
pub fn spawn_bee_workers_from_bee_hive(
    mut commands: Commands,
    mut query: Query<(Entity, &GlobalTransform, &mut BeeHive)>,
    animals_assets: AnimalsAssetSystemParams,
) {
    for (bee_hive_entity, bee_hive_global_transform, mut hive) in query.iter_mut() {
        let total_workers_to_spawn =
            (hive.current_honey / hive.honey_cost_per_worker_to_spawn).floor() as usize;

        for _ in 0..total_workers_to_spawn {
            if hive.get_bees_count() >= hive.max_workers {
                break;
            }

            hive.current_honey -= hive.honey_cost_per_worker_to_spawn;
            hive.current_honey = hive.current_honey.max(0.0);

            let Some(bee_model) = animals_assets.get_model(AnimalsAssetsKeys::BEE) else {
                log::warn!("Failed to load bee model");
                return;
            };

            let scale = 0.03;
            let physics_scale = 1.0;

            // Generate a random position for the bee
            let bee_hive_pos = bee_hive_global_transform.translation();

            let max_distance = 0.1;
            let spawn_position = bee_hive_pos
                + Vec3::new(
                    rand::thread_rng().gen_range(-max_distance..max_distance),
                    rand::thread_rng().gen_range(-0.2..0.2),
                    rand::thread_rng().gen_range(-max_distance..max_distance),
                );

            let bee = Bee {
                acceleration_speed: rand::thread_rng().gen_range(75.0..100.0),
                max_nectar: rand::thread_rng().gen_range(0.5..2.0),
                ..default()
            };

            let mut stimulus_sources = StimulusSources::new();

            let data = StimulusData::Food {
                nutrition_value: rand::thread_rng().gen_range(1.0..3.0),
                food_type: FoodType::Insect,
                is_consumable: true,
            };

            stimulus_sources.add(
                StimulusSource::new(StimulusType::Smell, 1.0).with_data(data.clone()),
            );
            stimulus_sources.add(
                StimulusSource::new(StimulusType::Visual, 0.85).with_data(data.clone()),
            );

            let gender = POSSIBLE_GENDERS.choose(&mut rand::thread_rng()).unwrap();

            // Create a spawn builder with common components
            let bee_entity = commands
                .spawn((
                    bee_model,
                    bee.clone(),
                    HomeBeeHive(bee_hive_entity),
                    Transform::from_translation(spawn_position)
                        .with_scale(Vec3::splat(scale)),
                    BiologicalOrganism,
                    Gender::new(gender.clone()).with_can_reproduce(false),
                    Stamina::default().with_current_stamina(1000.0),
                    Thirst::default().with_thirst_rate(0.1),
                    Hunger::default()
                        .with_hunger_rate(0.1) // Bees lose hunger slower than other organisms
                        .with_current_hunger(100.0),
                    LastVisitedPlants::default(),
                ))
                // AI components
                .insert((NeuralTrainingData::default(), NeuralBrain::new_for_bee()))
                // Perception components
                .insert((
                    SightPerception {
                        fov: 280.,
                        sight_radius: 40.0,
                        lose_sight_radius: 50.0,
                        peripheral_vision: 1.,
                        ..default()
                    },
                    SmellPerception {
                        smell_range: 50.0,
                        sensitivity: 1.0,
                        ..default()
                    },
                    AIPerception {
                        active_senses: vec![SenseType::Sight, SenseType::Smell],
                        short_term_memory_duration: 10.0,
                        long_term_memory_duration: 1000.0,
                        ..default()
                    },
                ))
                .insert(stimulus_sources)
                .insert((
                    // Physics components
                    RigidBody::Kinematic,
                    LinearVelocity::ZERO,
                    GravityScale(0.0),
                    Mass(0.1),
                    MaxLinearSpeed(bee.acceleration_speed),
                    MaxAngularSpeed(bee.acceleration_speed / 2.),
                    Collider::cuboid(physics_scale, physics_scale, physics_scale),
                    DebugRender::default().without_axes().without_collider(),
                ))
                .insert(MovementDampingFactor(0.9))
                // .insert(crate::components::perception::ShowDebugPerceptionGizmos)
                .id();

            // Spawn an entity to run the behavior tree
            commands
                .spawn((
                    Name::new("Behavior Tree"),
                    BehaveTree::new(create_neural_needs_behavior_tree(&bee))
                        .with_logging(false),
                    BehaveTargetEntity::Entity(bee_entity),
                ))
                .insert(ChildOf(bee_entity));
        }
    }
}
