use crate::resources::animals::AnimalsAssets;
use bevy::asset::Assets;
use bevy::ecs::system::SystemParam;
use bevy::gltf::Gltf;
use bevy::prelude::Res;
use bevy_gltf_animation::gltf_scene::GltfSceneRoot;

#[derive(SystemParam)]
pub struct AnimalsAssetSystemParams<'w> {
    animal_assets: Res<'w, AnimalsAssets>,
    gltf_assets: Res<'w, Assets<Gltf>>,
}

impl<'w> AnimalsAssetSystemParams<'w> {
    pub fn get_model(&self, name: &str) -> Option<GltfSceneRoot> {
        self.animal_assets.get_model(name, &self.gltf_assets)
    }
}
