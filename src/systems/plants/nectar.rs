use crate::components::environment::{Terrain, TerrainPod};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};
use crate::components::plants::{
    AnimalPollinatedPlant, NectarProduction, Plant, PlantGrowthStage
};
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;

/// System to update nectar production for animal-pollinated plants
pub fn update_nectar_production(
    mut query: Query<
        (
            &PlantGrowthStage,
            &Transform,
            &mut NectarProduction,
            Option<&CurrentTerrainPod>,
        ),
        (With<Plant>, With<AnimalPollinatedPlant>, Without<Dying>),
    >,
    terrain_query: Query<&Terrain>,
    terrain_pods_query: Query<&TerrainPod>,
    time: Res<WorldSimulationDeltaTime>,
) {
    for (growth_stage, plant_transform, mut nectar, current_pod) in query.iter_mut() {
        // Only mature plants produce nectar
        if *growth_stage != PlantGrowthStage::Mature {
            nectar.is_producing = false;
            nectar.current_nectar = 0.0;
            continue;
        }

        // Plants need moisture to produce nectar
        let mut terrain_moisture = 0.;

        // Try to get terrain information based on the plant's position
        // First check if the plant has a CurrentTerrainPod component
        if let Some(current_pod) = current_pod {
            // Get the TerrainPod component from the pod entity
            if let Ok(terrain_pod) = terrain_pods_query.get(current_pod.pod_entity) {
                // If the TerrainPod has a TerrainGrid, use it to find the terrain at the plant's position
                if let Some(terrain_grid) = &terrain_pod.terrain_grid {
                    let plant_pos = plant_transform.translation;

                    // Convert world position to grid coordinates within the pod
                    let (grid_x, grid_z) = terrain_grid.world_to_grid(plant_pos);

                    // Get the terrain entity at these coordinates
                    if let Some(&terrain_entity) =
                        terrain_grid.cells.get(&(grid_x, grid_z))
                    {
                        // Get the terrain properties
                        if let Ok(terrain) = terrain_query.get(terrain_entity) {
                            // Get the moisture from the terrain properties
                            terrain_moisture = terrain.properties.moisture.max(0.001);
                        }
                    }
                }
            }
        }

        #[cfg(debug_assertions)] {
            if terrain_moisture <= 0.0 {
                terrain_moisture = 0.001;
            }
        }

        if terrain_moisture > 0. {
            nectar.is_producing = true;

            // Produce nectar at the production rate
            let nectar_amount = nectar.production_rate * time.0;
            nectar.current_nectar =
                (nectar.current_nectar + nectar_amount).min(nectar.max_nectar);
        } else {
            nectar.is_producing = false;
        }
    }
}

/// System to handle animal pollination (bees, etc.)
pub fn animal_pollination(
    mut commands: Commands,
    mut query: Query<
        (
            Entity,
            &mut NectarProduction,
            &mut crate::components::plants::pollination::PollinationStatus,
        ),
        (With<Plant>, With<AnimalPollinatedPlant>, Without<Dying>),
    >,
) {
    for (entity, nectar, mut pollination_status) in query.iter_mut() {
        // If a bee has collected nectar (nectar is below max), mark as pollinated
        if nectar.is_producing
            && nectar.current_nectar < nectar.max_nectar
            && pollination_status.ready_for_pollination
        {
            // This is a simplification - in reality, this would be triggered by bee interaction
            // For now, we'll just assume that if nectar is being produced and some has been taken,
            // then the plant has been visited by a pollinator

            if !pollination_status.is_pollinated {
                pollination_status.is_pollinated = true;
                pollination_status.time_since_pollination = 0.0;

                // In a real implementation, we would also add pollen from another plant here
                // For now, we'll just mark it as pollinated
                commands
                    .entity(entity)
                    .insert(crate::components::plants::pollination::Pollinated);

                log::info!("Animal pollination successful for plant {entity:?}");
            }
        }
    }
}
