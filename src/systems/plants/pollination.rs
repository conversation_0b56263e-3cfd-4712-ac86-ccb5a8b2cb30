use crate::components::animals::{LastVisitedPlants, Pollinator};
use crate::components::ecosystem::FoodType;
use crate::components::genetics::{GeneticTrait, Genome};
use crate::components::lifecycle::{Dying, Gender};
use crate::components::perception::{
    StimulusData, StimulusSource, StimulusSources, StimulusType
};
use crate::components::plants::pollination::*;
use crate::components::plants::{
    AnimalPollinatedPlant, Plant, PlantGrowthStage, PlantPollinationType
};
use crate::events::plants_events::PlantPollinated;
use crate::resources::environment::Climate;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::TerrainSystemParams;
use crate::systems::plants::queries::PlantPollinationQuery;
use avian3d::prelude::*;
use bevy::prelude::*;
use rand::Rng;

/// System to update plant pollination status
pub fn update_pollination_status(
    mut query: Query<(&PlantGrowthStage, &mut PollinationStatus), With<Plant>>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (growth_stage, mut pollination) in query.iter_mut() {
        // Only mature plants can be pollinated
        pollination.ready_for_pollination = *growth_stage == PlantGrowthStage::Mature;

        // Update time since last pollination
        if !pollination.is_pollinated {
            pollination.time_since_pollination += delta_time.0;
        }
    }
}

/// System to handle natural pollination (wind, etc.)
pub fn natural_wind_pollination(
    mut commands: Commands,
    mut query: Query<
        (
            Entity,
            &GlobalTransform,
            &Genome,
            &mut PollinationStatus,
            &PlantPollinationType,
        ),
        (With<Plant>, Without<Dying>),
    >,
    pollen_query: Query<(Entity, &PhysicalPollen, &GlobalTransform)>,
    mut pollination_events: EventWriter<PlantPollinated>,
) {
    // Check each plant for potential pollination
    for (target_entity, target_transform, _, mut pollination_status, pollination_type) in
        query.iter_mut()
    {
        // Skip if not ready or already pollinated
        if !pollination_status.ready_for_pollination || pollination_status.is_pollinated {
            continue;
        }

        // Only wind-pollinated plants can be pollinated by physical pollen
        if *pollination_type != PlantPollinationType::WindPollinated {
            continue;
        }

        // Natural pollination chance increases with time since last attempt
        let base_chance = 0.01 * pollination_status.time_since_pollination;

        let mut rng = rand::thread_rng();

        // Find nearby pollen sources
        for (pollen_entity, pollen, pollen_pos) in pollen_query.iter() {
            // Skip self-pollination
            if target_entity == pollen.source_plant {
                continue;
            }

            // Calculate distance
            let distance = target_transform
                .translation()
                .distance(pollen_pos.translation());

            // Pollination chance decreases with distance
            let distance_factor = (0.1 - distance).max(0.0) / 0.1;
            let pollination_chance =
                base_chance * distance_factor * pollination_status.pollen_strength;

            if rng.gen::<f32>() < pollination_chance {
                // log::info!(
                //     "Natural pollination successful: {target_entity:?} by {:?}",
                //     pollen.source_plant
                // );

                // Successful pollination!
                pollination_status.is_pollinated = true;
                pollination_status.time_since_pollination = 0.0;

                // Store the source genome for reproduction
                commands.entity(target_entity).insert(Pollen {
                    source_plant: pollen.source_plant.clone(),
                    source_genome: pollen.source_genome.clone(),
                    viability: pollen.viability,
                });

                // Send event
                pollination_events.write(PlantPollinated {
                    target_entity,
                    source_entity: pollen.source_plant,
                });

                // Remove the pollen from the world
                commands.entity(pollen_entity).try_despawn();
                break;
            }
        }
    }
}

/// System to release pollen from mature plants
// Note: Add a mechanism that to limit how much pollen can be produced since it is finite.
pub fn release_pollen(
    mut commands: Commands,
    plant_query: Query<PlantPollinationQuery, With<Plant>>,
    terrain_system_params: TerrainSystemParams,
    time: Res<WorldSimulationDeltaTime>,
    climate: Res<Climate>,
) {
    for plant in plant_query.iter() {
        let crate::systems::plants::queries::PlantPollinationQueryItem {
            entity,
            global_transform: plant_transform,
            genome,
            growth_stage,
            life_span,
            phenotype,
            pollination_status,
            pollination_type,
            current_pod,
        } = plant;

        // Only mature plants release pollen
        if *growth_stage != PlantGrowthStage::Mature {
            continue;
        }

        // Plants do not release pollen when they're currently pollinated
        if pollination_status.is_pollinated {
            continue;
        }

        // Only wind-pollinated plants release pollen into the air
        if *pollination_type != PlantPollinationType::WindPollinated {
            continue;
        }

        // Find the terrain at the plant's position
        let terrain_pos = plant_transform.translation();

        // Check if there's moisture in the terrain
        let mut terrain_moisture = 0.0;

        // Try to get terrain information based on the plant's position
        // First check if the plant has a CurrentTerrainPod component
        if let Some(current_pod) = current_pod {
            // Get the pod entity
            let pod_entity = current_pod.pod_entity;
            terrain_moisture =
                terrain_system_params.get_terrain_moisture(pod_entity, terrain_pos);
        }

        // Plants can't produce pollen if there's no moisture in the terrain
        if terrain_moisture <= 0.0 {
            continue;
        }

        // Get pollen production rate from phenotype
        let pollen_production =
            phenotype.get_trait_value(&GeneticTrait::PollenProduction);

        // Chance to release pollen based on production rate, terrain moisture, and climate factors
        let release_chance = pollen_production
            * 0.5
            * time.0
            * (climate.base_temperature / 30.0)
            * climate.humidity.max(0.01)
            * terrain_moisture; // Factor in terrain moisture

        let mut rng = rand::thread_rng();
        if rng.gen::<f32>() < release_chance {
            // log::info!("Released pollen from plant {entity:?}");

            // Determine number of pollen particles to release
            let pollen_count = (pollen_production * 3.0).round() as usize;

            for _ in 0..pollen_count {
                // Calculate spawn position (slightly above the plant)
                let offset = Vec3::new(
                    rng.gen_range(-0.1..0.1),
                    0.05 + rng.gen_range(0.0..0.01),
                    rng.gen_range(-0.1..0.1),
                );

                let spawn_pos = offset;
                let viability = 1.0 - (life_span.normalized_age() * 0.8);

                let pollen = PhysicalPollen {
                    source_plant: entity,
                    source_genome: genome.clone(),
                    viability,
                    max_lifetime: 10.0 * rng.gen_range(0.1..2.0), // Randomize lifetime a bit
                    ..default()
                };

                // Spawn physical pollen entity
                let mut entity_commands = commands.spawn((
                    pollen.clone(),
                    RigidBody::Dynamic,
                    DebugRender::default().without_axes(),
                    Collider::sphere(0.003),
                    Friction::new(1.0),
                    Mass(pollen.mass),
                    LinearVelocity::ZERO,
                    Transform::from_translation(spawn_pos),
                    Name::new("Pollen"),
                ));

                let food_data = StimulusData::Food {
                    nutrition_value: 1.0,
                    food_type: FoodType::Pollen,
                    is_consumable: false,
                };

                let stimulus_sources = StimulusSources::with_sources(vec![
                    StimulusSource::new(StimulusType::Smell, 0.5)
                        .with_data(food_data.clone()),
                    StimulusSource::new(StimulusType::Visual, 0.2)
                        .with_data(food_data.clone()),
                ]);

                entity_commands.insert(stimulus_sources);

                if let Some(current_pod) = current_pod {
                    entity_commands.insert(ChildOf(current_pod.pod_entity));
                }
            }
        }
    }
}

/// System to update physical pollen lifetime and remove expired pollen
pub fn update_pollen_lifetime(
    mut commands: Commands,
    mut query: Query<(Entity, &GlobalTransform, &mut PhysicalPollen)>,
    time: Res<WorldSimulationDeltaTime>,
) {
    for (entity, transform, mut pollen) in query.iter_mut() {
        // Update lifetime
        pollen.lifetime += time.0;

        // Update viability (decreases over time)
        pollen.viability = 1.0 - (pollen.lifetime / pollen.max_lifetime);

        // Remove pollen if it has expired
        if pollen.lifetime >= pollen.max_lifetime || transform.translation().y < -10.0 {
            commands.entity(entity).try_despawn();
        }
    }
}

/// System to handle animal pollination (bees, butterflies, etc.)
pub fn animal_pollination(
    mut commands: Commands,
    mut plant_query: Query<
        (Entity, Option<&Gender>, &mut PollinationStatus),
        (With<Plant>, With<AnimalPollinatedPlant>, Without<Dying>),
    >,
    pollinator_query: Query<(Entity, &LastVisitedPlants), With<Pollinator>>,
    plant_genome_query: Query<&Genome, With<Plant>>,
    mut pollination_events: EventWriter<PlantPollinated>,
) {
    // First, collect all plants that have been visited by pollinators
    let mut pollinator_visits: std::collections::HashMap<Entity, Vec<(Entity, f32)>> =
        std::collections::HashMap::new();

    // Track which plants each pollinator has visited
    for (pollinator_entity, last_visited) in pollinator_query.iter() {
        for visited in last_visited.0.iter() {
            pollinator_visits
                .entry(visited.entity)
                .or_insert_with(Vec::new)
                .push((pollinator_entity, visited.visited_time));
        }
    }

    // Process each animal-pollinated plant
    for (plant_entity, gender, mut pollination_status) in plant_query.iter_mut() {
        if let Some(gender) = gender {
            if !gender.can_reproduce {
                continue; // Skip plants that aren't reproductively mature
            }
        }

        // Skip if not ready or already pollinated
        if !pollination_status.ready_for_pollination || pollination_status.is_pollinated {
            continue;
        }

        // Check if this plant has been visited by any pollinators
        if let Some(visitors) = pollinator_visits.get(&plant_entity) {
            // This plant has been visited by pollinators
            let mut rng = rand::thread_rng();

            // Base chance of pollination increases with number of visitors
            let base_chance = 0.2 * visitors.len() as f32;

            // Check if pollination occurs
            if rng.gen::<f32>() < base_chance * pollination_status.pollen_strength {
                // Find another plant that the same pollinator has visited
                let mut source_plant: Option<Entity> = None;
                let mut source_genome: Option<Genome> = None;

                // Look through all pollinators that visited this plant
                'pollinator_loop: for &(pollinator, _) in visitors {
                    // Check other plants this pollinator has visited
                    for (other_plant, _other_visited_time) in
                        pollinator_visits.iter().filter_map(|(plant, visits)| {
                            // Skip the current plant
                            if *plant == plant_entity {
                                return None;
                            }

                            // Find visits by the current pollinator
                            visits
                                .iter()
                                .find(|(visitor, _)| *visitor == pollinator)
                                .map(|(_, time)| (*plant, *time))
                        })
                    {
                        // Found another plant visited by the same pollinator
                        // Try to get its genome
                        if let Ok(other_genome) = plant_genome_query.get(other_plant) {
                            source_plant = Some(other_plant);
                            source_genome = Some(other_genome.clone());
                            break 'pollinator_loop;
                        }
                    }
                }

                // If we found a source plant and genome, pollinate this plant
                if let (Some(source_entity), Some(genome)) = (source_plant, source_genome)
                {
                    // Successful pollination!
                    pollination_status.is_pollinated = true;
                    pollination_status.time_since_pollination = 0.0;

                    // Store the source genome for reproduction
                    commands.entity(plant_entity).insert(Pollen {
                        source_plant: source_entity,
                        source_genome: genome,
                        viability: 1.0, // Assume full viability for animal pollination
                    });

                    // Send event
                    pollination_events.write(PlantPollinated {
                        target_entity: plant_entity,
                        source_entity,
                    });

                    // log::info!("Animal pollination successful: {plant_entity:?} by {source_entity:?}");
                }
            }
        }
    }
}
