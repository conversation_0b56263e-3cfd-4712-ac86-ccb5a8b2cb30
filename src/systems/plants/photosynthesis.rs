use crate::components::environment::{Sun, Terrain, TerrainPod};
use crate::components::genetics::{GeneticTrait, Phenotype};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};
use crate::components::plants::photosynthesis::{EnergyConsumption, LightAbsorption};
use crate::components::plants::{Plant, PlantAge, PlantGrowthStage};
use crate::resources::{DayPhase, TimeOfDay, WorldSimulationDeltaTime};
use crate::systems::environment::system_params::TerrainSystemParams;
use crate::systems::plants::queries::PlantPhotosynthesisQuery;
use bevy::prelude::*;

/// System to calculate light absorption for plants based on sun position and intensity
pub fn calculate_light_absorption(
    mut plant_query: Query<(&Transform, &mut LightAbsorption, &Phenotype), With<Plant>>,
    sun_query: Query<(&Transform, &DirectionalLight), With<Sun>>,
    time: Res<TimeOfDay>,
) {
    // Skip calculation during night
    if time.phase() == DayPhase::Night {
        for (_, mut absorption, _) in plant_query.iter_mut() {
            absorption.current_absorption = 0.05; // Minimal absorption at night
        }
        return;
    }

    // Get sun position and intensity
    if let Ok((sun_transform, sun_light)) = sun_query.single() {
        let sun_direction = sun_transform.forward();
        let sun_intensity = sun_light.illuminance / 10000.0; // Normalize to 0-1 range

        for (plant_transform, mut absorption, phenotype) in plant_query.iter_mut() {
            // Calculate how directly the sun is hitting the plant (dot product of up vector and sun direction)
            // This simulates the angle of the sun relative to the plant
            let plant_up = plant_transform.up();
            let sun_angle_factor = plant_up.dot(-sun_direction.as_vec3()).max(0.01);

            // Get the photosynthesis efficiency from the plant's phenotype
            let efficiency =
                phenotype.get_trait_value(&GeneticTrait::PhotosynthesisEfficiency);

            // Get the leaf size from the plant's phenotype (affects absorption area)
            let leaf_size = phenotype.get_trait_value(&GeneticTrait::LeafSize);

            // Calculate the final absorption rate
            let absorption_rate =
                sun_intensity * sun_angle_factor * efficiency * leaf_size;

            // Update the plant's light absorption
            absorption.current_absorption = absorption_rate;
        }
    }
}

struct Test {
    query: PlantPhotosynthesisQuery,
}

/// System to convert absorbed light into energy through photosynthesis
pub fn perform_photosynthesis(
    mut plant_query: Query<
        (PlantPhotosynthesisQuery, Option<&CurrentTerrainPod>),
        (With<Plant>, Without<Dying>),
    >,
    terrain_system_params: TerrainSystemParams,
    time: Res<WorldSimulationDeltaTime>,
) {
    for (mut plant, current_pod) in plant_query.iter_mut() {
        // Find the terrain at the plant's position
        let terrain_pos = plant.global_transform.translation();

        // Get terrain moisture
        let mut terrain_moisture = 0.1;

        if let Some(current_pod) = current_pod {
            let pod_entity = current_pod.pod_entity;
            terrain_moisture =
                terrain_system_params.get_terrain_moisture(pod_entity, terrain_pos);
        }

        // Calculate energy production based on light absorption and moisture
        // Plants need moisture for photosynthesis - reduce efficiency when moisture is low
        let moisture_factor = if terrain_moisture <= 0.0 {
            0.2 // Minimal photosynthesis even with no moisture (using internal reserves)
        } else {
            0.5 + (terrain_moisture * 0.5) // Scale with moisture level
        };

        let energy_production = plant.absorption.current_absorption
            * plant.absorption.efficiency
            * (moisture_factor * plant.fitness.value * 2.)
            * time.0;

        // Calculate energy consumption based on growth stage
        let base_consumption = plant.energy.base_rate * time.0;
        let growth_consumption = match *plant.growth_stage {
            PlantGrowthStage::Seed => 0.0, // Seeds use minimal energy
            PlantGrowthStage::Sprout => plant.energy.growth_rate * time.0, // Sprouts use more energy for growth
            PlantGrowthStage::Mature => plant.energy.base_rate * 0.25 * time.0, // Mature plants use less energy for maintenance
        };

        // Add reproduction energy cost for mature plants
        // This represents the energy investment in preparing for reproduction
        let reproduction_consumption = match *plant.growth_stage {
            PlantGrowthStage::Mature => {
                plant.energy.get_reproduction_rate() * 0.1 * time.0
            } // Mature plants invest some energy in reproduction
            _ => 0.0, // Non-mature plants don't invest in reproduction
        };

        // Calculate total energy consumption
        let total_consumption =
            base_consumption + growth_consumption + reproduction_consumption;

        // Determine if we need to use stored energy (at night or when production is low)
        let energy_deficit = if energy_production < total_consumption {
            total_consumption - energy_production
        } else {
            0.0
        };

        // Use stored energy if needed and available
        let energy_from_storage = energy_deficit.min(plant.absorption.energy_stored);
        plant.absorption.energy_stored -= energy_from_storage;

        // Calculate excess energy to store
        let excess_energy = if energy_production > total_consumption {
            energy_production - total_consumption
        } else {
            0.0
        };

        // Store excess energy up to max capacity
        plant.absorption.energy_stored += excess_energy;
        plant.absorption.energy_stored = plant
            .absorption
            .energy_stored
            .min(plant.absorption.max_energy);

        // Calculate net energy change for this update
        let net_energy_change =
            (energy_production + energy_from_storage) - total_consumption;

        // Store the net energy change for use in other systems
        plant.energy.net_energy_change = net_energy_change;
    }
}

/// System to apply energy to plant growth
pub fn apply_energy_to_growth(
    mut query: Query<
        (
            &CurrentTerrainPod,
            &EnergyConsumption,
            &mut PlantAge,
            &LightAbsorption,
            &Transform,
        ),
        (With<Plant>, Without<Dying>),
    >,
    terrain_query: Query<&Terrain>,
    terrain_pods_query: Query<&TerrainPod>,
    time: Res<WorldSimulationDeltaTime>,
) {
    for (current_pod, energy, mut age, absorption, transform) in query.iter_mut() {
        let Some(terrain_pod) = terrain_pods_query.get(current_pod.pod_entity).ok()
        else {
            continue;
        };

        let Some(terrain_grid) = &terrain_pod.terrain_grid else {
            continue;
        };

        // Find the terrain at the plant's position
        let terrain_pos = transform.translation;
        let (grid_x, grid_z) =
            terrain_grid.world_to_grid(Vec3::new(terrain_pos.x, 0.0, terrain_pos.z));

        // Get terrain moisture
        let mut terrain_moisture = 0.0;
        if let Some(&terrain_entity) = terrain_grid.cells.get(&(grid_x, grid_z)) {
            if let Ok(terrain) = terrain_query.get(terrain_entity) {
                terrain_moisture = terrain.properties.moisture;
            }
        }

        // Only grow if we have positive net energy or stored energy
        if energy.net_energy_change > 0.0 || absorption.energy_stored > 5.0 {
            // Calculate growth boost based on net energy and stored energy
            let net_energy_factor = if energy.net_energy_change > 0.0 {
                energy.net_energy_change * 0.5
            } else {
                0.0
            };

            // Consider stored energy as a backup growth factor
            // Plants with more stored energy can still grow even during night
            let storage_factor = (absorption.energy_stored / absorption.max_energy) * 0.2;

            // Apply moisture factor to growth
            // Plants need moisture to grow - reduce growth when moisture is low
            let moisture_factor = if terrain_moisture <= 0.0 {
                0.1 // Minimal growth with no moisture (survival mode)
            } else {
                0.5 + (terrain_moisture * 0.5) // Scale with moisture level
            };

            // Apply growth boost with moisture factor
            let growth_boost = (net_energy_factor + storage_factor) * moisture_factor;
            age.0 += time.0 * growth_boost;
        }
    }
}
