use crate::components::genetics::{Fitness, GeneticTrait, Phenotype};
use crate::components::plants::pollination::PhysicalPollen;
use crate::components::plants::render::PlantSprite;
use crate::components::plants::*;
use crate::components::GameEntityRender;
use crate::post_process::pixelated_material::{
    ExtendedPixelatedMaterial, PixelatedMaterial
};
use crate::resources::plants::{constants, PlantAssets, PlantModelType};
use avian3d::prelude::*;
use bevy::gltf::GltfMaterialName;
use bevy::prelude::*;
use bevy::render::render_resource::Face;
use bevy_sprite3d::{Sprite3dBuilder, Sprite3dParams};
use bevy_tween::combinator::{sequence, tween};
use bevy_tween::interpolate::Scale;
use bevy_tween::prelude::*;
use bevy_tween::tween::{AnimationTarget, TargetComponent};
use std::ops::Sub;

pub fn render_spawned_plants(
    mut commands: Commands,
    mut sprite_params: Sprite3dParams,
    query: Query<(Entity, &PlantProperties), Added<Plant>>,
    assets: Res<PlantAssets>,
    asset_server: Res<AssetServer>,
) {
    for (plant_entity, data) in query.iter() {
        match data.0.model_type {
            PlantModelType::Sprite => {
                // Handle 2D sprite rendering
                let Some(handle) = assets.sprites.get(&data.0.get_sprite_key()) else {
                    continue;
                };

                let sprite_bundle = Sprite3dBuilder {
                    image: handle.clone(),
                    pixels_per_metre: 500.,
                    alpha_mode: AlphaMode::Blend,
                    unlit: false,
                    pivot: Some(Vec2::new(0.5, 0.0)),
                    ..default()
                }
                .bundle(&mut sprite_params);

                let target_scale = 1.1;
                let tween_forward = ComponentTween::new_target(
                    TargetComponent::marker(),
                    Scale {
                        start: Vec3::splat(1.),
                        end: Vec3::new(target_scale, 1., target_scale),
                    },
                );

                let tween_backward = ComponentTween::new_target(
                    TargetComponent::marker(),
                    Scale {
                        start: Vec3::new(target_scale, 1., target_scale),
                        end: Vec3::splat(1.),
                    },
                );

                // Random animation duration
                let duration = 0.5 + rand::random::<f32>() * 0.5;

                // Spawn the sprite at the plant's position with scaling
                commands
                    .spawn((
                        Name::new("Sprite Render"),
                        GameEntityRender,
                        PlantSprite,
                        sprite_bundle,
                        AnimationTarget,
                    ))
                    .insert(ChildOf(plant_entity))
                    .animation()
                    .repeat(Repeat::Infinitely)
                    .insert(sequence((
                        tween(secs(duration), EaseKind::BounceInOut, tween_forward),
                        tween(secs(duration), EaseKind::BounceInOut, tween_backward),
                    )));
            }
            PlantModelType::Model => {
                // Spawn the model as a child of the plant entity
                commands
                    .spawn((
                        GameEntityRender,
                        Name::new("Model Render"),
                        SceneRoot(asset_server.load(
                            GltfAssetLabel::Scene(0).from_asset(data.0.get_model_path()),
                        )),
                        Transform::from_scale(Vec3::splat(0.25)),
                    ))
                    .insert(ChildOf(plant_entity));
            }
        }
    }
}

/// System to set materials for spawned tree models
pub fn set_spawned_tree_materials(
    asset_server: Res<AssetServer>,
    mut commands: Commands,
    mut materials: ResMut<Assets<ExtendedPixelatedMaterial>>,
    query: Query<(Entity, &GltfMaterialName), Added<MeshMaterial3d<StandardMaterial>>>,
) {
    for (entity, material_name) in query.iter() {
        let material_name = material_name.0.to_lowercase();
        let is_bark = material_name.contains("_bark");
        let is_leaves = material_name.contains("_leaves");

        let mut material = None;
        if is_bark {
            material = Some(StandardMaterial {
                base_color: Color::srgb(0.35, 0.22, 0.15).lighter(0.1), // Richer brown color
                metallic: 0.0,               // Bark isn't metallic
                perceptual_roughness: 0.9,   // Bark is rough
                reflectance: 0.1,            // Minimal reflectance
                double_sided: false,         // Typically single-sided
                cull_mode: Some(Face::Back), // Cull backfaces
                ..default()
            });
        }

        if is_leaves {
            let leaves_color = Color::srgb(0.15, 0.45, 0.1).lighter(0.1);
            material = Some(StandardMaterial {
                base_color_texture: Some(asset_server.load("images/leaves/leaves_6.png")),
                // base_color: Color::WHITE, // Rich green color
                base_color: leaves_color.into(), // Rich green color
                emissive: leaves_color.into(), // Slight emissive for a glowy effect
                metallic: 0.0,             // Leaves aren't metallic
                perceptual_roughness: 0.7, // Leaves have some roughness
                reflectance: 0.2,          // Slight reflectance for waxy leaves
                alpha_mode: AlphaMode::Blend, // For semi-transparent leaves
                double_sided: false,       // Leaves should be visible from both sides
                // cull_mode: None,  // Don't cull faces for leaves
                // cull_mode: Some(Face::Back),
                ..default()
            })
        }

        if let Some(material) = material {
            let mut entity_commands = commands.entity(entity);

            if is_leaves {
                let target_scale = 0.98;
                let tween_forward = ComponentTween::new_target(
                    TargetComponent::marker(),
                    Scale {
                        start: Vec3::splat(1.),
                        end: Vec3::new(target_scale, 1., target_scale),
                    },
                );

                let tween_backward = ComponentTween::new_target(
                    TargetComponent::marker(),
                    Scale {
                        start: Vec3::new(target_scale, 1., target_scale),
                        end: Vec3::splat(1.),
                    },
                );

                // Random animation duration
                let duration = 6.0 + rand::random::<f32>() * 0.5;

                entity_commands
                    .insert(AnimationTarget)
                    .remove::<MeshMaterial3d<StandardMaterial>>()
                    .insert(MeshMaterial3d(
                        materials.add(PixelatedMaterial::new(material, 8)),
                    ))
                    .animation()
                    .repeat(Repeat::Infinitely)
                    .insert(sequence((
                        tween(secs(duration), EaseKind::SineInOut, tween_forward),
                        tween(secs(duration), EaseKind::SineInOut, tween_backward),
                    )));
            } else if is_bark {
                entity_commands
                    .remove::<MeshMaterial3d<StandardMaterial>>()
                    .insert(MeshMaterial3d(
                        materials.add(PixelatedMaterial::new(material, 4)),
                    ));
            }
        }
    }
}

fn secs(secs: f32) -> Duration {
    Duration::from_secs_f32(secs)
}

const TREE_SPECIES: [&str; 1] = [constants::species::SPECIES_BIRCH];

pub fn update_plant_sprites(
    mut query: Query<
        (
            &PlantSpeciesName,
            &PlantAge,
            &PlantGrowthStage,
            &Fitness,
            &Phenotype,
            &mut Transform,
        ),
        With<Plant>,
    >,
) {
    for (species_name, age, growth_stage, fitness, phenotype, mut transform) in
        query.iter_mut()
    {
        // Ignore trees
        if TREE_SPECIES.contains(&species_name.0.to_lowercase().as_str()) {
            continue;
        }

        // Get genetic factors that affect plant size
        let stem_height = phenotype.get_trait_value(&GeneticTrait::StemHeight);
        let leaf_size = phenotype.get_trait_value(&GeneticTrait::LeafSize);

        // Age factor - plants grow gradually within their growth stage
        // Calculate how far along the plant is in its current stage
        let age_factor = match growth_stage {
            PlantGrowthStage::Seed => 1.0 + age.0.min(2.5) / 5.0, // Full size at age 5
            PlantGrowthStage::Sprout => 2.5 + (age.0.min(30.0) / 30.0) * 0.5, // Additional 50% growth during sprout stage
            PlantGrowthStage::Mature => 3.5 + (age.0.sub(30.0).min(100.0) / 100.0) * 0.5, // Additional 50% growth during mature stage
        };

        // Fitness affects overall size (healthier plants are bigger)
        let fitness_factor = 0.8 + (fitness.value * 0.4); // Range: 0.8 to 1.2

        // Calculate final scale
        let final_scale = stem_height * leaf_size * age_factor * fitness_factor;

        // Apply the calculated scale to the plant's transform
        transform.scale = Vec3::splat(final_scale);
    }
}

/// System to render physical pollen particles
pub fn render_pollen(
    mut commands: Commands,
    query: Query<(Entity, &PhysicalPollen), Added<PhysicalPollen>>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for (entity, pollen) in query.iter() {
        let pollen_mesh = meshes.add(Sphere::new(0.003));

        commands.entity(entity).insert((
            RigidBody::Dynamic,
            Collider::sphere(0.003),
            Friction::new(1.0).with_dynamic_coefficient(1.0),
            Mass(pollen.mass),
            Mesh3d(pollen_mesh),
            // MeshMaterial3d(toon.add(crate::materials::toon_material::ToonMaterial {
            //     base_color: Color::srgb(1.0, 1.0, 1.0).to_linear(),
            //     ..default()
            // })),
            MeshMaterial3d(materials.add(StandardMaterial {
                base_color: Color::srgb(1.0, 1.0, 0.8),
                alpha_mode: AlphaMode::Blend,
                unlit: false,
                ..default()
            })),
        ));
    }
}
