mod helpers;
pub mod lifecycle;
pub mod nectar;
pub mod photosynthesis;
pub mod pollination;
mod queries;
pub mod render;
mod system_params;

use crate::components::ecosystem::FoodType;
use crate::components::environment::{
    BiomeType, Rock, SoilType, Terrain, TerrainCell, TerrainPod, TerrainType
};
use crate::components::genetics::{GeneticTrait, Genome, Phenotype};
use crate::components::lifecycle::{
    CurrentTerrainPod, Dying, Gender, Lifespan, Organism
};
use crate::components::perception::{
    StimulusData, StimulusSource, StimulusSources, StimulusType
};
use crate::components::plants::photosynthesis::{EnergyConsumption, LightAbsorption};
use crate::components::plants::pollination::{Pollen, Pollinated, PollinationStatus};
use crate::components::plants::{
    AnimalPollinatedPlant, Plant, PlantAge, PlantGrowthStage, PlantName, PlantPollinationType, PlantProperties, PlantSpeciesName, PlantTree, WindPollinatedPlant
};
use crate::components::GameID;
use crate::resources::environment::{
    EnvironmentAssets, EnvironmentAssetsKeys, TerrainGrid, TerrainPodGrid
};
use crate::resources::plants::constants;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::{
    EnvironmentAssetSystemParams, TerrainSystemParams
};
use crate::systems::plants::queries::PlantReproductionQuery;
use crate::systems::plants::system_params::PlantsAssetSystemParams;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use rand::prelude::ThreadRng;
use rand::Rng;
use std::ops::{Div, Range};

/// System to update plant growth based on genetics and environment
pub fn update_plant_growth(
    mut query: Query<
        (&mut PlantAge, &Phenotype, &Transform, Option<&CurrentTerrainPod>),
        (With<Plant>, Without<Dying>),
    >,
    terrain_query: Query<&Terrain>,
    terrain_pods_query: Query<&TerrainPod>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut plant_age, phenotype, transform, current_pod) in query.iter_mut() {
        // Get growth rate from phenotype
        let growth_rate = phenotype.get_trait_value(&GeneticTrait::GrowthRate);

        // Find the terrain at the plant's position
        let terrain_pos = transform.translation;
        let mut adjusted_growth_rate = growth_rate;

        // Try to get terrain information based on the plant's position
        // First check if the plant has a CurrentTerrainPod component
        if let Some(current_pod) = current_pod {
            // Get the pod entity
            let pod_entity = current_pod.pod_entity;

            // Get the TerrainPod component from the pod entity
            if let Ok(terrain_pod) = terrain_pods_query.get(pod_entity) {
                // If the TerrainPod has a TerrainGrid, use it to find the terrain at the plant's position
                if let Some(terrain_grid) = &terrain_pod.terrain_grid {
                    // Convert world position to grid coordinates within the pod
                    let (grid_x, grid_z) = terrain_grid.world_to_grid(terrain_pos);

                    // Get the terrain entity at these coordinates
                    if let Some(&terrain_entity) =
                        terrain_grid.cells.get(&(grid_x, grid_z))
                    {
                        // Get the terrain properties
                        if let Ok(terrain) = terrain_query.get(terrain_entity) {
                            // Adjust growth based on terrain properties
                            let properties = &terrain.properties;

                            // Water retention affects how effectively the plant can use moisture
                            let water_retention_factor =
                                0.7 + (properties.water_retention * 0.6);

                            // Moisture affects growth (0.5 is neutral)
                            let moisture_factor = 0.5 + properties.moisture;
                            adjusted_growth_rate *=
                                moisture_factor * water_retention_factor;

                            // Fertility affects growth (0.5 is neutral)
                            let fertility_factor = 0.5 + properties.fertility;
                            adjusted_growth_rate *= fertility_factor;
                        }
                    }
                } else {
                    // If the TerrainPod doesn't have a TerrainGrid, use the default growth rate
                    adjusted_growth_rate = growth_rate;
                }
            } else {
                // If we can't get the TerrainPod, use the default growth rate
                adjusted_growth_rate = growth_rate;
            }
        } else {
            // If the plant doesn't have a CurrentTerrainPod component, we can't determine the terrain
            // Just use the default growth rate
            adjusted_growth_rate = growth_rate;
        }

        // Apply the adjusted growth rate
        plant_age.0 += delta_time.0 * adjusted_growth_rate;
    }
}

/// System to apply genetic effects to plant growth
pub fn apply_genetics_to_plant_growth(
    mut query: Query<
        (&Phenotype, &mut PlantAge, &Transform, Option<&CurrentTerrainPod>),
        With<Plant>,
    >,
    terrain_query: Query<&Terrain>,
    terrain_pods_query: Query<&TerrainPod>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (phenotype, mut plant_age, transform, current_pod) in query.iter_mut() {
        // Get the growth rate modifier from genetics
        let growth_rate = phenotype.get_trait_value(&GeneticTrait::GrowthRate);

        // Find the terrain at the plant's position
        let terrain_pos = transform.translation;

        // Apply terrain effects to growth rate
        let mut adjusted_growth_rate = growth_rate;

        // Try to get terrain information based on the plant's position
        // First check if the plant has a CurrentTerrainPod component
        if let Some(current_pod) = current_pod {
            // Get the pod entity
            let pod_entity = current_pod.pod_entity;

            // Get the TerrainPod component from the pod entity
            if let Ok(terrain_pod) = terrain_pods_query.get(pod_entity) {
                // If the TerrainPod has a TerrainGrid, use it to find the terrain at the plant's position
                if let Some(terrain_grid) = &terrain_pod.terrain_grid {
                    // Convert world position to grid coordinates within the pod
                    let (grid_x, grid_z) = terrain_grid.world_to_grid(terrain_pos);

                    // Get the terrain entity at these coordinates
                    if let Some(&terrain_entity) =
                        terrain_grid.cells.get(&(grid_x, grid_z))
                    {
                        // Get the terrain properties
                        if let Ok(terrain) = terrain_query.get(terrain_entity) {
                            // Adjust growth based on terrain properties
                            let properties = &terrain.properties;

                            // Apply genetic traits related to environmental preferences

                            // Water preference - how well the plant does in current moisture conditions
                            let water_preference =
                                phenotype.get_trait_value(&GeneticTrait::WaterPreference);
                            let moisture_match =
                                1.0 - (water_preference - properties.moisture).abs();
                            adjusted_growth_rate *= 0.7 + (moisture_match * 0.6);
                        }
                    }
                } else {
                    // If the TerrainPod doesn't have a TerrainGrid, use the default growth rate
                    adjusted_growth_rate = growth_rate;
                }
            } else {
                // If we can't get the TerrainPod, use the default growth rate
                adjusted_growth_rate = growth_rate;
            }
        } else {
            // If the plant doesn't have a CurrentTerrainPod component, we can't determine the terrain
            // Just use the default growth rate
            adjusted_growth_rate = growth_rate;
        }

        // Apply the adjusted growth rate
        plant_age.0 += delta_time.0 * adjusted_growth_rate;
    }
}

/// System to handle reproduction and inheritance
pub fn handle_reproduction(
    mut commands: Commands,
    mut query: Query<(PlantReproductionQuery, Option<&Gender>), With<Plant>>,
    terrain_system_params: TerrainSystemParams,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    let pod_grid = terrain_system_params.terrain_pod_grid;

    for (plant, gender) in query.iter_mut() {
        // Only mature plants can reproduce
        if *plant.growth_stage != PlantGrowthStage::Mature {
            continue;
        }

        // Check if the plant has a gender component and is reproductively mature
        if let Some(gender) = gender {
            if !gender.can_reproduce || !gender.is_mature() {
                continue; // Skip plants that aren't reproductively mature
            }
        }

        let mut energy = plant.light_absorption;

        // Plants need excess energy to reproduce
        // Factor in the reproduction_rate as a cost that affects reproduction chance
        let mut reproduction_cost =
            plant.energy_consumption.get_reproduction_rate() * energy.max_energy;

        // Factor in the plant's fitness
        reproduction_cost *= 1.0 / plant.fitness.value;

        // Calculate energy factor considering reproduction cost
        let energy_factor = if energy.energy_stored > reproduction_cost {
            // More excess energy means higher chance to reproduce
            (energy.energy_stored - reproduction_cost) / reproduction_cost
        } else if energy.energy_stored > 0.0 {
            // Some energy but not enough for optimal reproduction
            energy.energy_stored / (reproduction_cost * 2.0)
        } else {
            0.0 // No energy, no reproduction
        };

        // Higher chance if pollinated
        let pollination_factor = if plant.pollen.is_some() { 3.0 } else { 1.0 };

        // Calculate reproduction chance based on fitness, energy, and pollination
        let reproduction_chance = 0.01
            * plant.fitness.value
            * energy_factor
            * pollination_factor
            * delta_time.0;

        let mut rng = rand::thread_rng();

        if rng.gen::<f32>() < reproduction_chance
            && energy.energy_stored > reproduction_cost
        {
            let cross_pollinate = plant
                .phenotype
                .get_trait_value(&GeneticTrait::CrossPollination)
                == 1.0;

            // If cross-pollination is required and no pollen is present, skip reproduction
            if cross_pollinate && plant.pollen.is_none() {
                continue;
            }

            let mut child_genome = if let Some(pollen) = plant.pollen {
                // Cross the genomes if pollinated
                Genome::cross(plant.genome, &pollen.source_genome)
            } else {
                // Self-pollination - clone with mutations
                let mut self_genome = plant.genome.clone();
                self_genome.mutate(plant.genome.mutation_rate.div(2.0));
                self_genome
            };

            // Add some random mutations
            child_genome.mutate(plant.genome.mutation_rate);

            // New position should be a random offset from the parent's position
            let offset = 0.2;
            let scatter = Vec3::new(
                rng.gen_range(-offset..offset),
                0.0,
                rng.gen_range(-offset..offset),
            );

            // Calculate new position with scatter
            let mut new_position = plant.transform.translation + scatter;

            // Get the pod index for the current position
            let pod_index = pod_grid.world_to_cell(plant.transform.translation);

            // Get the pod size
            let pod_width = pod_grid.get_terrain_max_width();
            let pod_depth = pod_grid.get_terrain_max_depth();

            // Calculate the world position of the pod
            let pod_world_pos = pod_grid.cell_to_world(pod_index.0, pod_index.1);

            // Check if the new position is within the current pod bounds
            if new_position.x < pod_world_pos.x
                || new_position.x > pod_world_pos.x + pod_width
                || new_position.z < pod_world_pos.z
                || new_position.z > pod_world_pos.z + pod_depth
            {
                // If not, adjust the position to be within the pod bounds
                new_position.x = new_position.x.clamp(
                    pod_world_pos.x + offset,
                    pod_world_pos.x + pod_width - offset,
                );
                new_position.z = new_position.z.clamp(
                    pod_world_pos.z + offset,
                    pod_world_pos.z + pod_depth - offset,
                );
            }

            // Create a new plant with the child genome
            let child_id = spawn_plant_with_genome(
                &mut commands,
                new_position,
                child_genome,
                plant.properties,
            );

            let mut entity_cmds = commands.entity(child_id);

            if plant.wind_pollinated.is_none() {
                entity_cmds.remove::<WindPollinatedPlant>();
            }

            // Copy the current terrain pod
            if let Some(current_pod) = plant.current_pod {
                entity_cmds.insert(current_pod.clone());
                entity_cmds.insert(ChildOf(current_pod.pod_entity));
            }

            entity_cmds
                .insert(plant.energy_consumption.clone().reset())
                .insert(Name::new(format!(
                    "(Plant) {}-{}",
                    plant.name.0,
                    child_id.index()
                )))
                .insert(PlantName(plant.name.0.clone()))
                .insert(plant.properties.clone())
                .insert(plant.species_name.clone());

            // Deduct the reproduction cost from the plant's energy
            energy.energy_stored -= reproduction_cost;
            energy.energy_stored = energy.energy_stored.max(0.0);

            // log::info!("Plant reproduced: {child_id:?} | Energy: {energy:?} | Reproduction cost: {reproduction_cost}");

            // Reset the pollination status
            commands
                .entity(plant.entity)
                .remove::<Pollen>()
                .remove::<Pollinated>()
                .insert(plant.pollination_status.clone().reset());
        }
    }
}

const SPAWN_HEIGHT_OFFSET: f32 = 0.5;

/// Helper function to spawn a plant with a specific genome
pub fn spawn_plant_with_genome(
    commands: &mut Commands,
    position: Vec3,
    genome: Genome,
    plant: &PlantProperties,
) -> Entity {
    let plant = plant.0.clone();

    // Calculate the phenotype from the genome
    let phenotype = Phenotype::from_genome(&genome);

    let game_id = GameID::new();
    let mut rotation = Quat::from_rotation_y(-std::f32::consts::FRAC_PI_4);

    // Also rotate to face upward at 45 degrees
    rotation *= Quat::from_rotation_x(-46.5_f32.to_radians());
    // log::info!("Spawned a new plant {game_id:?} with inherited genetics");

    // Calculate base lifespan based on phenotype
    let longevity_factor = phenotype.get_trait_value(&GeneticTrait::Longevity);

    let base_lifespan = genome.base_lifespan; // Base lifespan for plants
    let mut max_age = base_lifespan * longevity_factor;

    // Slightly vary the lifespan to add variety
    max_age *= 0.6 + rand::random::<f32>() * 0.4;

    let light_absorption = LightAbsorption {
        max_energy: genome.base_max_energy * (0.8 + rand::random::<f32>() * 0.4),
        efficiency: phenotype.get_trait_value(&GeneticTrait::PhotosynthesisEfficiency),
        ..default()
    };

    let energy_consumption = EnergyConsumption {
        base_rate: 0.1,
        growth_rate: phenotype.get_trait_value(&GeneticTrait::GrowthRate),
        ..default()
    };

    // log::info!("Genome: {genome:?} | Lifespan Max Age: {max_age} | Longevity Factor: {longevity_factor}");

    // Create stimulus sources for the plant
    let mut stimulus_sources = StimulusSources::new();

    // Visual stimulus for plants
    stimulus_sources.add(StimulusSource::new(StimulusType::Visual, 0.5));

    // Food stimulus for plants - they can be eaten
    if plant.nectar_production.is_some() {
        stimulus_sources.add(StimulusSource::new(StimulusType::Smell, 0.7).with_data(
            StimulusData::Food {
                nutrition_value: 15.0, // Base nutrition value (TODO: Make this a genetic trait)
                food_type: FoodType::Nectar,
                is_consumable: true,
            },
        ));
    }

    // Create a spawn builder with common components
    let mut entity_cmds = commands.spawn((
        Name::new("Plant"),
        Plant,
        Organism,
        PlantGrowthStage::Seed,
        game_id.clone(),
        genome,
        phenotype,
        energy_consumption,
        light_absorption,
        Transform::from_translation(position.clone()).with_rotation(rotation),
        Lifespan::new(max_age),
        stimulus_sources,
    ));

    entity_cmds.insert(plant.gender.clone());
    entity_cmds.insert(plant.energy_consumption.clone());
    entity_cmds.insert(Name::new(format!("(Plant) {}", plant.name)));
    entity_cmds.insert(PlantName(plant.name.clone()));
    entity_cmds.insert(PlantProperties(plant.clone()));
    entity_cmds.insert(PollinationStatus::default());

    match plant.pollination_type {
        PlantPollinationType::AnimalPollinated => {
            entity_cmds
                .remove::<WindPollinatedPlant>()
                .insert(AnimalPollinatedPlant);

            // Add nectar production if it's an animal pollinated plant
            if let Some(nectar_prod) = &plant.nectar_production {
                entity_cmds.insert(nectar_prod.clone());
            }
        }
        PlantPollinationType::WindPollinated => {
            entity_cmds.insert(WindPollinatedPlant);
        }
    }

    if plant.is_tree {
        entity_cmds.insert(PlantTree);
    }

    entity_cmds.id()
}

/// System to spawn plants
pub fn spawn_plants(
    mut commands: Commands,
    environment_assets: EnvironmentAssetSystemParams,
    pod_grid: Res<TerrainPodGrid>,
    terrain: Query<(Entity, &Terrain, &TerrainCell, &Aabb), (With<Terrain>, Added<Aabb>)>,
    plant_assets: PlantsAssetSystemParams,
) {
    for (terrain_entity, terrain, terrain_cell, aabb) in terrain.iter() {
        match terrain.biome {
            BiomeType::Water(_) => continue,
            _ => {}
        }

        let mut rng = rand::thread_rng();
        let should_spawn = rng.gen_bool(0.9);

        // Randomly decide whether to spawn plants on this terrain
        if !should_spawn {
            continue;
        }

        // Function to spawn rocks - takes commands by reference to avoid multiple mutable borrows
        fn spawn_rocks_fn(
            commands: &mut Commands,
            environment_assets: &EnvironmentAssetSystemParams,
            terrain_entity: Entity,
            aabb: &Aabb,
            max_count: usize,
            rng: &mut ThreadRng,
        ) {
            for _ in 0..max_count {
                let target_pos = Vec3::new(
                    rng.gen_range(-aabb.half_extents.x..aabb.half_extents.x),
                    0.25,
                    rng.gen_range(-aabb.half_extents.z..aabb.half_extents.z),
                );

                if let Some(scene_root) = environment_assets.get_random_rock_model() {
                    let mut random_scale = rng.gen_range(0.05..0.15);
                    if rng.gen_bool(0.1) {
                        random_scale = rng.gen_range(0.2..0.25);
                    }
                    commands
                        .spawn((
                            Name::new("Rock"),
                            Rock,
                            scene_root,
                            Transform::from_scale(Vec3::splat(random_scale))
                                .with_translation(target_pos),
                        ))
                        .insert(ChildOf(terrain_entity));
                }
            }
        }

        // Function to spawn plants - takes commands by reference
        fn spawn_plants_fn(
            commands: &mut Commands,
            pod_grid: &Res<TerrainPodGrid>,
            plant_assets: &PlantsAssetSystemParams,
            terrain_entity: Entity,
            terrain_cell: &TerrainCell,
            aabb: &Aabb,
        ) {
            const SPECIES_AND_PLANTS: [(&str, &str); 3] = [
                (
                    constants::species::SPECIES_MOONFLORA,
                    constants::flowers::moonflora::MOONFLOWER,
                ),
                (constants::species::SPECIES_GRASS, constants::grass::GRASS_14),
                (constants::species::SPECIES_GRASS, constants::grass::GRASS_15),
            ];

            for (species_name, plant_name) in SPECIES_AND_PLANTS.iter() {
                if spawn_plant_from_species_and_name(
                    commands,
                    pod_grid,
                    plant_assets,
                    terrain_entity,
                    terrain_cell,
                    aabb,
                    species_name,
                    plant_name,
                    // Range { start: 1, end: rand::random::<i32>().abs() % 3 + 1 },
                    Range { start: 1, end: 3 },
                ) {
                    continue;
                }
            }
        }

        // Function to spawn trees - takes commands by reference
        fn spawn_trees_fn(
            commands: &mut Commands,
            pod_grid: &Res<TerrainPodGrid>,
            plant_assets: &PlantsAssetSystemParams,
            terrain_entity: Entity,
            terrain_cell: &TerrainCell,
            aabb: &Aabb,
        ) -> bool {
            if let Some((tree_species, tree_name)) =
                constants::trees::get_random_tree().map(|x| (x.species, x.name))
            {
                return spawn_plant_from_species_and_name(
                    commands,
                    pod_grid,
                    plant_assets,
                    terrain_entity,
                    terrain_cell,
                    aabb,
                    tree_species,
                    tree_name,
                    Range { start: 1, end: 3 },
                );
            }

            false
        }

        match terrain.terrain_type {
            TerrainType::Water(_) => {
                spawn_rocks_fn(
                    &mut commands,
                    &environment_assets,
                    terrain_entity,
                    aabb,
                    1,
                    &mut rng,
                );
            }
            TerrainType::Soil(SoilType::Loam) => {
                // let mut rng = rand::thread_rng();
                // if rng.gen_bool(0.1) {
                //     spawn_rocks_fn(
                //         &mut commands,
                //         &environment_assets,
                //         terrain_entity,
                //         aabb,
                //         3,
                //         &mut rng,
                //     );
                // }

                if rng.gen_bool(0.4) {
                    spawn_plants_fn(
                        &mut commands,
                        &pod_grid,
                        &plant_assets,
                        terrain_entity,
                        terrain_cell,
                        aabb,
                    );
                }

                if rng.gen_bool(0.1) {
                    spawn_trees_fn(
                        &mut commands,
                        &pod_grid,
                        &plant_assets,
                        terrain_entity,
                        terrain_cell,
                        aabb,
                    );
                }
            }
            _ => {}
        }
    }
}

fn spawn_plant_from_species_and_name(
    mut commands: &mut Commands,
    pod_grid: &Res<TerrainPodGrid>,
    plant_assets: &PlantsAssetSystemParams,
    terrain_entity: Entity,
    terrain_cell: &TerrainCell,
    aabb: &Aabb,
    species_name: &str,
    plant_name: &str,
    range: Range<i32>,
) -> bool {
    let Some((plant, species)) = plant_assets.get_plant(species_name, plant_name) else {
        log::warn!("Failed to load plant: {species_name} - {plant_name}");
        return true;
    };

    let mut rng = rand::thread_rng();
    let extents = aabb.half_extents;
    let x_extent = extents.x - 0.05;
    let z_extent = extents.z - 0.05;

    let total_plants = rng.gen_range(range);
    for _ in 0..total_plants {
        // Random x and z position with terrain size
        let x = rng.gen_range(-x_extent..x_extent);
        let z = rng.gen_range(-z_extent..z_extent);

        let position = Vec3::new(x, 0. + plant.model_height_offset, z);

        let entity = spawn_plant_with_genome(
            &mut commands,
            position,
            plant.genome.clone(),
            &PlantProperties(plant.clone()),
        );

        let mut entity_cmds = commands.entity(entity);

        let id = entity_cmds
            .insert(PlantSpeciesName(species.name.clone()))
            .insert(ChildOf(terrain_entity))
            .id();

        let pod_index = terrain_cell.pod_index;
        if let Some(&pod_entity) = pod_grid.get_pod(pod_index.0, pod_index.1) {
            commands
                .entity(id)
                .insert(CurrentTerrainPod::new(pod_entity, pod_index));
            // log::info!("Assigned plant to pod ({}, {})", pod_index.0, pod_index.1);
        }
    }

    false
}
