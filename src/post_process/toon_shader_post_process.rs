use crate::components::environment::Sun;
use bevy::pbr::{MaterialP<PERSON>eline, MaterialPipelineKey};
use bevy::prelude::*;
use bevy::render::mesh::MeshVertexBufferLayoutRef;
use bevy::render::render_asset::RenderAssets;
use bevy::render::render_resource::{
    AsBindGroup, AsBindGroupShaderType, RenderPipelineDescriptor, ShaderRef, ShaderType, SpecializedMeshPipelineError
};
use bevy::render::texture::GpuImage;

#[derive(Default)]
pub struct ToonShaderPlugin;

impl Plugin for ToonShaderPlugin {
    fn build(&self, app: &mut App) {
        app.add_plugins(MaterialPlugin::<ToonShaderMaterial>::default())
            .register_asset_reflect::<ToonShaderMaterial>()
            .add_systems(Update, update_toon_shader);
    }
}

#[derive(Asset, AsBindGroup, Reflect, Debug, <PERSON><PERSON>, Default)]
#[reflect(De<PERSON>ult, Debug)]
#[uniform(0, ToonShaderMaterialUniform)]
pub struct ToonShaderMaterial {
    pub color: Color,
    pub sun_dir: Vec3,
    pub sun_color: Color,
    pub camera_pos: Vec3,
    pub ambient_color: Color,
    #[texture(1)]
    #[sampler(2)]
    pub base_color_texture: Option<Handle<Image>>,
    pub alpha_mode: AlphaMode,
}

impl Material for ToonShaderMaterial {
    fn fragment_shader() -> ShaderRef {
        "shaders/toon_shader.wgsl".into()
    }

    fn alpha_mode(&self) -> AlphaMode {
        self.alpha_mode
    }

    fn specialize(
        _pipeline: &MaterialPipeline<Self>,
        descriptor: &mut RenderPipelineDescriptor,
        _layout: &MeshVertexBufferLayoutRef,
        _key: MaterialPipelineKey<Self>,
    ) -> Result<(), SpecializedMeshPipelineError> {
        if let Some(frag) = descriptor.fragment.as_mut() {
            //frag.shader_defs
        };

        Ok(())
    }
}

impl AsBindGroupShaderType<ToonShaderMaterialUniform> for ToonShaderMaterial {
    fn as_bind_group_shader_type(
        &self,
        _images: &RenderAssets<GpuImage>,
    ) -> ToonShaderMaterialUniform {
        ToonShaderMaterialUniform {
            color: LinearRgba::from(self.color).to_f32_array().into(),
            sun_dir: self.sun_dir,
            sun_color: LinearRgba::from(self.sun_color).to_f32_array().into(),
            camera_pos: self.camera_pos,
            ambient_color: LinearRgba::from(self.ambient_color).to_f32_array().into(),
        }
    }
}

#[derive(Clone, Default, ShaderType)]
pub struct ToonShaderMaterialUniform {
    pub color: Vec4,
    pub sun_dir: Vec3,
    pub sun_color: Vec4,
    pub camera_pos: Vec3,
    pub ambient_color: Vec4,
}

#[derive(Component)]
pub struct ToonShaderMainCamera;

pub fn update_toon_shader(
    sun: Query<(&Transform, &DirectionalLight), With<Sun>>,
    ambient_light: Option<Res<AmbientLight>>,
    mut toon_materials: ResMut<Assets<ToonShaderMaterial>>,
) {
    for (_, toon_mat) in toon_materials.iter_mut() {
        if let Ok((sun_t, dir_light)) = sun.single() {
            toon_mat.sun_dir = *sun_t.back();
            toon_mat.sun_color = dir_light.color.into();
        }

        if let Some(light) = &ambient_light {
            toon_mat.ambient_color = light.color.into();
        }
    }
}
