use bevy::prelude::*;
use crate::components::animals::AnimalAnimationIndex;

/// Component that tracks the current animation state of an organism
#[derive(Component, Reflect, Debug, Clone, PartialEq)]
#[reflect(Component)]
pub struct AnimationState {
    /// The current animation being played
    pub current_animation: OrganismAnimation,
    /// The previous animation (for transition logic)
    pub previous_animation: Option<OrganismAnimation>,
    /// Time since the current animation started
    pub time_in_current_animation: f32,
    /// Whether the animation should loop
    pub should_loop: bool,
    /// Speed multiplier for the animation
    pub speed_multiplier: f32,
}

impl Default for AnimationState {
    fn default() -> Self {
        Self {
            current_animation: OrganismAnimation::Idle,
            previous_animation: None,
            time_in_current_animation: 0.0,
            should_loop: true,
            speed_multiplier: 1.0,
        }
    }
}

impl AnimationState {
    pub fn new(animation: OrganismAnimation) -> Self {
        Self {
            current_animation: animation,
            ..default()
        }
    }

    pub fn transition_to(&mut self, new_animation: OrganismAnimation) {
        if self.current_animation != new_animation {
            self.previous_animation = Some(self.current_animation.clone());
            self.current_animation = new_animation;
            self.time_in_current_animation = 0.0;
        }
    }

    pub fn update_time(&mut self, delta_time: f32) {
        self.time_in_current_animation += delta_time;
    }
}

/// Enum representing different animation states for organisms
#[derive(Debug, Clone, PartialEq, Reflect)]
pub enum OrganismAnimation {
    /// Organism is idle (standing still)
    Idle,
    /// Organism is sitting
    Sit,
    /// Organism is laying down
    Lay,
    /// Organism is walking
    Walk,
    /// Organism is running
    Run,
    /// Organism is swimming
    Swim,
    /// Organism is eating
    Eat,
    /// Organism is dying/dead
    Death,
    /// Organism is attacking
    Attack,
    /// Organism is in fear state
    Fear,
    /// Organism is jumping
    Jump,
    /// Organism is flying (for bees)
    Fly,
}

impl OrganismAnimation {
    /// Get the animation index for this animation type
    pub fn get_animation_index(&self) -> usize {
        match self {
            OrganismAnimation::Idle => AnimalAnimationIndex::IDLE_A,
            OrganismAnimation::Sit => AnimalAnimationIndex::SIT,
            OrganismAnimation::Lay => AnimalAnimationIndex::SIT, // Use sit for lay if no lay animation
            OrganismAnimation::Walk => AnimalAnimationIndex::WALK,
            OrganismAnimation::Run => AnimalAnimationIndex::RUN,
            OrganismAnimation::Swim => AnimalAnimationIndex::SWIM,
            OrganismAnimation::Eat => AnimalAnimationIndex::EAT,
            OrganismAnimation::Death => AnimalAnimationIndex::DEATH,
            OrganismAnimation::Attack => AnimalAnimationIndex::ATTACK,
            OrganismAnimation::Fear => AnimalAnimationIndex::FEAR,
            OrganismAnimation::Jump => AnimalAnimationIndex::JUMP,
            OrganismAnimation::Fly => AnimalAnimationIndex::FLY,
        }
    }

    /// Get the priority of this animation (higher number = higher priority)
    pub fn get_priority(&self) -> u8 {
        match self {
            OrganismAnimation::Death => 100,
            OrganismAnimation::Attack => 90,
            OrganismAnimation::Fear => 85,
            OrganismAnimation::Eat => 80,
            OrganismAnimation::Jump => 75,
            OrganismAnimation::Swim => 70,
            OrganismAnimation::Run => 60,
            OrganismAnimation::Walk => 50,
            OrganismAnimation::Fly => 45,
            OrganismAnimation::Sit => 20,
            OrganismAnimation::Lay => 15,
            OrganismAnimation::Idle => 10,
        }
    }

    /// Check if this animation should loop
    pub fn should_loop(&self) -> bool {
        match self {
            OrganismAnimation::Death => false,
            OrganismAnimation::Jump => false,
            OrganismAnimation::Attack => false,
            _ => true,
        }
    }
}

/// Configuration for animation speed thresholds
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct AnimationConfig {
    /// Velocity threshold below which the organism is considered idle
    pub idle_threshold: f32,
    /// Velocity threshold above which the organism switches from walk to run
    pub run_threshold: f32,
    /// Base speed multiplier for walk animations
    pub walk_speed_multiplier: f32,
    /// Base speed multiplier for run animations  
    pub run_speed_multiplier: f32,
    /// How often to randomly change idle animations (in seconds)
    pub idle_variation_interval: f32,
    /// Time since last idle variation change
    pub time_since_idle_change: f32,
}

impl Default for AnimationConfig {
    fn default() -> Self {
        Self {
            idle_threshold: 0.1,
            run_threshold: 3.0,
            walk_speed_multiplier: 1.0,
            run_speed_multiplier: 1.5,
            idle_variation_interval: 10.0,
            time_since_idle_change: 0.0,
        }
    }
}

impl AnimationConfig {
    pub fn new(idle_threshold: f32, run_threshold: f32) -> Self {
        Self {
            idle_threshold,
            run_threshold,
            ..default()
        }
    }

    pub fn update_idle_timer(&mut self, delta_time: f32) {
        self.time_since_idle_change += delta_time;
    }

    pub fn should_change_idle_animation(&mut self) -> bool {
        if self.time_since_idle_change >= self.idle_variation_interval {
            self.time_since_idle_change = 0.0;
            true
        } else {
            false
        }
    }
}
