pub mod animation_state;
pub mod bees;
pub mod deer;

use crate::components::lifecycle::*;
use avian3d::math::<PERSON>ala<PERSON>;
use bevy::prelude::*;
use bevy_descendant_collector::EntityCollectorTarget;
use bevy_inspector_egui::prelude::*;
use bevy_inspector_egui::InspectorOptions;
use noise::NoiseFn;
use rand::Rng;

pub trait AnimalMovementBehavior {
    fn get_target_distance(&self) -> f32;

    fn get_speed(&self) -> f32 {
        1.0
    }

    fn calculate_velocity_towards_target(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity_towards_target_with_speed(dt, target, current, 1.0)
    }

    fn calculate_velocity_towards_target_with_speed(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3);
}

#[derive(Component, Reflect)]
#[reflect(Component)]
pub struct MovementDampingFactor(pub Scalar);

#[derive(Component)]
pub struct AutoRigColliders;

pub trait AnimalBaseArmature {
    fn get_base(&self) -> Entity;
    fn get_root(&self) -> Entity;
    fn get_body(&self) -> Entity;
    fn get_ear_l(&self) -> Entity;
    fn get_ear_r(&self) -> Entity;
    fn get_mesh(&self) -> Entity;
}

#[derive(Component, EntityCollectorTarget, Reflect, InspectorOptions)]
#[reflect(Component, InspectorOptions)]
#[name_path("Rig")] // This is only used when the root has to be automatically discovered, like for scenes
pub struct AnimalQuadrupedBaseArmature {
    #[name_path()]
    pub base: Entity,
    #[name_path("root")]
    pub root: Entity,
    #[name_path("root", "body")]
    pub body: Entity,
    #[name_path("root", "body", "ear.L")]
    pub ear_l: Entity,
    #[name_path("root", "body", "ear.R")]
    pub ear_r: Entity,
    #[name_path("Mesh")]
    pub mesh: Entity,
}

impl AnimalBaseArmature for AnimalQuadrupedBaseArmature {
    fn get_base(&self) -> Entity {
        self.base
    }
    fn get_root(&self) -> Entity {
        self.root
    }
    fn get_body(&self) -> Entity {
        self.body
    }
    fn get_ear_l(&self) -> Entity {
        self.ear_l
    }
    fn get_ear_r(&self) -> Entity {
        self.ear_r
    }
    fn get_mesh(&self) -> Entity {
        self.mesh
    }
}

#[allow(non_snake_case, non_upper_case_globals)]
pub mod AnimalAnimationIndex {
    pub const ATTACK: usize = 0;
    pub const BOUNCE: usize = 1;
    pub const CLICKED: usize = 2;
    pub const DEATH: usize = 3;
    pub const EAT: usize = 4;
    pub const FEAR: usize = 5;
    pub const FLY: usize = 6;
    pub const HIT: usize = 7;
    pub const IDLE_A: usize = 8;
    pub const TURN_LEFT: usize = 9;
    pub const TURN_RIGHT: usize = 10;
    pub const JUMP: usize = 11;
    pub const ROLL: usize = 12;
    pub const RUN: usize = 13;
    pub const SIT: usize = 14;
    pub const SPIN_SPLASH: usize = 15;
    pub const SWIM: usize = 16;
    pub const WALK: usize = 17;
}

#[derive(Component)]
#[component(storage = "SparseSet")]
pub struct Grounded;

#[derive(Component, Default)]
pub struct Pollinator;

#[derive(Reflect, Clone, Debug)]
pub struct VisitedPlant {
    pub entity: Entity,
    pub visited_time: f32,
    pub visited_count: u32,
    pub last_nectar_amount: f32,
}

#[derive(Component, Reflect, Clone, Debug, Default)]
#[reflect(Component)]
pub struct LastVisitedPlants(pub Vec<VisitedPlant>);

impl LastVisitedPlants {
    pub fn add(&mut self, entity: Entity, time: f32, last_nectar_amount: f32) {
        // If we already have this plant, update the visited time and count
        if let Some(plant) = self.0.iter_mut().find(|p| p.entity == entity) {
            plant.visited_time = time;
            plant.visited_count += 1;
            plant.last_nectar_amount = last_nectar_amount;
            return;
        }

        // Retain only the last recent plants
        self.0.retain(|p| p.visited_time > time - 60.0);

        self.0.push(VisitedPlant {
            entity,
            visited_time: time,
            visited_count: 1,
            last_nectar_amount,
        });
    }

    pub fn last_visit_time(&self, entity: Entity) -> Option<f32> {
        self.0
            .iter()
            .find(|p| p.entity == entity)
            .map(|p| p.visited_time)
    }

    pub fn get_last_nectar(&self, entity: Entity) -> f32 {
        self.0
            .iter()
            .find(|p| p.entity == entity)
            .map(|p| p.last_nectar_amount)
            .unwrap_or(0.0)
    }
}
