use bevy::prelude::*;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>)]
#[require(Lifespan)]
pub struct Organism;

/// Component that tracks which TerrainPod an organism is currently in
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct CurrentTerrainPod {
    /// The entity ID of the terrain pod
    pub pod_entity: Entity,
    /// The grid coordinates of the pod
    pub pod_index: (i32, i32),
}

impl CurrentTerrainPod {
    pub fn new(pod_entity: Entity, pod_index: (i32, i32)) -> Self {
        Self {
            pod_entity,
            pod_index,
        }
    }
}

/// Component that tracks an entity's lifespan
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct Lifespan {
    /// The maximum age this entity can reach (in simulation time)
    pub max_age: f32,
    /// The current age of the entity (in simulation time)
    pub current_age: f32,
    /// Whether this entity can die of old age
    pub can_die_of_old_age: bool,
    /// The health of the entity (0.0 - 1.0), affects how well it handles aging
    pub health: f32,
}

impl Default for Lifespan {
    fn default() -> Self {
        Self {
            max_age: 10_000.0,
            current_age: 0.0,
            can_die_of_old_age: true,
            health: 1.0,
        }
    }
}

impl Lifespan {
    pub fn new(max_age: f32) -> Self {
        Self {
            max_age,
            current_age: 0.0,
            can_die_of_old_age: true,
            health: 1.0,
        }
    }

    /// Returns the normalized age (0.0 - 1.0) representing how far along the lifespan this entity is
    pub fn normalized_age(&self) -> f32 {
        self.current_age / self.max_age
    }

    /// Returns true if the entity has reached the end of its lifespan
    pub fn is_at_end_of_life(&self) -> bool {
        self.can_die_of_old_age && self.current_age >= self.max_age
    }

    /// Decreases the entity's health by the given amount
    pub fn decrease_health(&mut self, amount: f32) {
        self.health -= amount;
        self.health = self.health.clamp(0.0, 1.0);
    }

    /// Increases the entity's health by the given amount
    pub fn increase_health(&mut self, amount: f32) {
        self.health += amount;
        self.health = self.health.clamp(0.0, 1.0);
    }

    /// Returns a value between 0.0 and 1.0 representing the entity's vitality
    /// 1.0 = young and healthy, 0.0 = old and frail
    pub fn vitality(&self) -> f32 {
        if self.max_age <= 0.0 {
            return 1.0;
        }

        let age_factor = 1.0 - (self.current_age / self.max_age).min(1.0);
        age_factor * self.health
    }
}

/// Component that marks an entity as dying
#[derive(Component, Debug, Clone)]
pub struct Dying {
    /// How long the dying process takes
    pub duration: f32,
    /// Time elapsed in the dying process
    pub elapsed: f32,
}

impl Default for Dying {
    fn default() -> Self {
        Self {
            duration: 10.0,
            elapsed: 0.0,
        }
    }
}

impl Dying {
    pub fn new(duration: f32) -> Self {
        Self {
            duration,
            elapsed: 0.0,
        }
    }

    /// Returns true if the dying process is complete
    pub fn is_complete(&self) -> bool {
        self.elapsed >= self.duration
    }

    /// Returns a value between 0.0 and 1.0 representing the progress of the dying process
    pub fn progress(&self) -> f32 {
        (self.elapsed / self.duration).min(1.0)
    }
}

/// Component that tracks an organism's stamina (energy for physical activities)
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct Stamina {
    /// The maximum stamina level this organism can have
    pub max_stamina: f32,
    /// The current stamina level
    pub current_stamina: f32,
    /// The rate at which stamina regenerates when resting (per second)
    pub regeneration_rate: f32,
    /// The rate at which stamina depletes during activity (per second)
    pub depletion_rate: f32,
}

impl Default for Stamina {
    fn default() -> Self {
        Self {
            max_stamina: 1000.0,
            current_stamina: 1000.0,
            regeneration_rate: 2.0,
            depletion_rate: 0.1,
        }
    }
}

impl Stamina {
    pub fn with_current_stamina(mut self, current_stamina: f32) -> Self {
        self.current_stamina = current_stamina.clamp(0.0, self.max_stamina);
        self
    }

    /// Returns the normalized stamina level (0.0 - 1.0)
    pub fn normalized_stamina(&self) -> f32 {
        self.current_stamina / self.max_stamina
    }

    /// Returns true if the organism is exhausted (stamina <= 0)
    pub fn is_exhausted(&self) -> bool {
        self.current_stamina <= 0.0
    }

    /// Depletes stamina by the given amount
    pub fn deplete(&mut self, dt: f32) {
        self.current_stamina -= self.depletion_rate * dt;
        self.current_stamina = self.current_stamina.max(0.0);
    }

    /// Regenerates stamina by the given amount
    pub fn regenerate(&mut self, dt: f32) {
        self.current_stamina += self.regeneration_rate * dt;
        self.current_stamina = self.current_stamina.min(self.max_stamina);
    }
}

/// Component that tracks an organism's hunger level
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct Hunger {
    /// The maximum hunger level (0.0 = starving, max_hunger = full)
    pub max_hunger: f32,
    /// The current hunger level (0.0 = starving, max_hunger = full)
    pub current_hunger: f32,
    /// The rate at which hunger increases over time (per second)
    pub hunger_rate: f32,
    /// The threshold below which the organism starts suffering health effects
    pub critical_threshold: f32,
    /// The amount of time the organism has been in a critical hunger state
    time_in_critical_state: f32,
    /// The duration after which an organism in critical state will die (in seconds)
    pub fatal_threshold_duration: f32,
}

impl Default for Hunger {
    fn default() -> Self {
        Self {
            max_hunger: 100.0,
            current_hunger: 75.0,
            hunger_rate: 0.5,
            critical_threshold: 10.0,
            time_in_critical_state: 0.0,
            fatal_threshold_duration: Thirst::default().fatal_threshold_duration * 3.,
        }
    }
}

impl Hunger {
    pub fn new(max_hunger: f32) -> Self {
        Self {
            max_hunger,
            current_hunger: max_hunger * 0.75,
            hunger_rate: 0.5,
            critical_threshold: max_hunger * 0.2,
            time_in_critical_state: 0.0,
            fatal_threshold_duration: 30.0,
        }
    }

    pub fn with_current_hunger(mut self, current_hunger: f32) -> Self {
        self.current_hunger = current_hunger.clamp(0.0, self.max_hunger);
        self
    }

    pub fn with_hunger_rate(mut self, hunger_rate: f32) -> Self {
        self.hunger_rate = hunger_rate;
        self
    }

    /// Returns true if the organism should die from starvation
    pub fn is_fatal(&self) -> bool {
        self.is_critical() && self.time_in_critical_state >= self.fatal_threshold_duration
    }

    /// Returns the normalized hunger level (0.0 = starving, 1.0 = full)
    pub fn normalized_hunger(&self) -> f32 {
        self.current_hunger / self.max_hunger
    }

    /// Returns true if the organism is in a critical hunger state
    pub fn is_critical(&self) -> bool {
        self.current_hunger <= self.critical_threshold
    }

    /// Returns true if the organism is starving (hunger <= 0)
    pub fn is_starving(&self) -> bool {
        self.current_hunger <= 0.0
    }

    /// Consumes food, increasing the hunger level
    pub fn consume_food(&mut self, nutrition_value: f32) {
        self.current_hunger += nutrition_value;
        self.current_hunger = self.current_hunger.min(self.max_hunger);

        // Reset critical state timer if we're above the threshold
        if self.current_hunger > self.critical_threshold {
            self.time_in_critical_state = 0.0;
        }
    }

    pub fn update(&mut self, dt: f32) {
        if self.is_critical() {
            self.time_in_critical_state += dt;
        }
    }

    pub fn get_time_in_critical_state(&self) -> f32 {
        self.time_in_critical_state
    }
}

/// Component that tracks an organism's thirst level
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct Thirst {
    /// The maximum thirst level (0.0 = dehydrated, max_thirst = hydrated)
    pub max_thirst: f32,
    /// The current thirst level (0.0 = dehydrated, max_thirst = hydrated)
    pub current_thirst: f32,
    /// The rate at which thirst increases over time (per second)
    pub thirst_rate: f32,
    /// The threshold below which the organism starts suffering health effects
    pub critical_threshold: f32,
    /// The amount of time the organism has been in a critical thirst state
    pub time_in_critical_state: f32,
    /// The duration after which an organism in critical state will die (in seconds)
    pub fatal_threshold_duration: f32,
}

impl Default for Thirst {
    fn default() -> Self {
        Self {
            max_thirst: 100.0,
            current_thirst: 80.0,
            thirst_rate: 0.8,
            critical_threshold: 10.0,
            time_in_critical_state: 0.0,
            fatal_threshold_duration: 200.0, // Dehydration is more severe than hunger
        }
    }
}

impl Thirst {
    pub fn new(max_thirst: f32) -> Self {
        Self {
            max_thirst,
            current_thirst: max_thirst * 0.8,
            thirst_rate: 0.8,
            critical_threshold: max_thirst * 0.15,
            time_in_critical_state: 0.0,
            fatal_threshold_duration: 20.0, // Dehydration is more severe than hunger
        }
    }

    pub fn with_thirst_rate(mut self, thirst_rate: f32) -> Self {
        self.thirst_rate = thirst_rate;
        self
    }

    pub fn with_current_thirst(mut self, current_thirst: f32) -> Self {
        self.current_thirst = current_thirst.clamp(0.0, self.max_thirst);
        self
    }

    /// Returns true if the organism should die from dehydration
    pub fn is_fatal(&self) -> bool {
        self.is_critical() && self.time_in_critical_state >= self.fatal_threshold_duration
    }

    /// Returns the normalized thirst level (0.0 = dehydrated, 1.0 = hydrated)
    pub fn normalized_thirst(&self) -> f32 {
        self.current_thirst / self.max_thirst
    }

    /// Returns true if the organism is in a critical thirst state
    pub fn is_critical(&self) -> bool {
        self.current_thirst <= self.critical_threshold
    }

    /// Returns true if the organism is dehydrated (thirst <= 0)
    pub fn is_dehydrated(&self) -> bool {
        self.current_thirst <= 0.0
    }

    /// Drinks water, increasing the thirst level
    pub fn drink(&mut self, water_amount: f32) {
        self.current_thirst += water_amount;
        self.current_thirst = self.current_thirst.min(self.max_thirst);

        // Reset critical state timer if we're above the threshold
        if self.current_thirst > self.critical_threshold {
            self.time_in_critical_state = 0.0;
        }
    }
}

/// Enum representing the biological gender of an organism
#[derive(serde::Deserialize, Reflect, Debug, Clone, Copy, PartialEq, Eq)]
pub enum BiologicalGender {
    Male,
    Female,
    Hermaphrodite, // For organisms that have both male and female reproductive capabilities
    Asexual,       // For organisms that reproduce asexually
}

impl Default for BiologicalGender {
    fn default() -> Self {
        Self::Asexual // Default to asexual for simplicity
    }
}

/// Component that represents the gender of a biological organism
#[derive(serde::Deserialize, Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct Gender {
    /// The biological gender of the organism
    #[serde(default = "default_gender")]
    pub gender: BiologicalGender,
    /// Whether this organism can reproduce
    #[serde(default = "default_can_reproduce")]
    pub can_reproduce: bool,
    /// The maturity level for reproduction (0.0 to 1.0)
    #[serde(default = "default_reproductive_maturity")]
    pub reproductive_maturity: f32,
    /// The age threshold at which the organism becomes reproductively mature (0.0 to 1.0)
    #[serde(default = "default_maturity_threshold")]
    pub maturity_threshold: f32,
}

fn default_can_reproduce() -> bool {
    Gender::default().can_reproduce
}

fn default_gender() -> BiologicalGender {
    Gender::default().gender
}

fn default_reproductive_maturity() -> f32 {
    Gender::default().reproductive_maturity
}

fn default_maturity_threshold() -> f32 {
    Gender::default().maturity_threshold
}

impl Default for Gender {
    fn default() -> Self {
        Self {
            gender: BiologicalGender::default(),
            can_reproduce: true,
            reproductive_maturity: 0.0,
            maturity_threshold: 0.01,
        }
    }
}

impl Gender {
    /// Create a new gender component with the specified gender
    pub fn new(gender: BiologicalGender) -> Self {
        Self {
            gender,
            ..default()
        }
    }

    pub fn with_can_reproduce(mut self, can_reproduce: bool) -> Self {
        self.can_reproduce = can_reproduce;
        self
    }

    /// Check if the organism is reproductively mature
    pub fn is_mature(&self) -> bool {
        self.reproductive_maturity >= self.maturity_threshold
    }

    /// Update the reproductive maturity based on age
    pub fn update_maturity(&mut self, normalized_age: f32, maturity_threshold: f32) {
        // Organisms become reproductively mature after reaching a certain age threshold
        self.reproductive_maturity = normalized_age * (1.0 - maturity_threshold);
        self.reproductive_maturity = self.reproductive_maturity.clamp(0.0, 1.0);
    }
}

#[derive(Component, Default, Debug, Clone)]
#[require(Organism, Lifespan, Stamina, Hunger, Thirst, Gender)]
pub struct BiologicalOrganism;
