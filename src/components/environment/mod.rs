pub mod grass;

use crate::components::lifecycle::CurrentTerrainPod;
use avian3d::prelude::*;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use std::collections::HashSet;

/// Component that marks an entity as a terrain cell
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
pub struct TerrainCell {
    /// The x coordinate in the terrain grid (local to the pod)
    pub x: i32,
    /// The z coordinate in the terrain grid (local to the pod)
    pub z: i32,
    /// The elevation of this cell (y-coordinate)
    pub elevation: f32,
    /// The pod this cell belongs to
    pub pod_index: (i32, i32),
}

#[derive(Reflect, Component, Debug)]
#[require(Transform, RigidBody, Collider)]
pub struct TerrainPodPhysicsFloor;

#[derive(Reflect, Component, Debug)]
#[require(Transform, RigidBody, Collider)]
pub struct TerrainPodPhysicsWall;

/// The different types of terrain that can exist in the world
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum TerrainType {
    // Soil types
    Soil(SoilType),
    // Water types
    Water(WaterType),
    // Other terrain types
    Rock,
    Sand,
}

impl Default for TerrainType {
    fn default() -> Self {
        TerrainType::Soil(SoilType::Loam)
    }
}

/// The different types of soil that can exist in the world
#[derive(Default, Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum SoilType {
    /// High in nutrients, retains water, but poor drainage
    Clay,
    /// Low in nutrients, poor water retention, good drainage
    Sandy,
    /// Balanced soil, good for most plants
    #[default]
    Loam,
    /// Medium nutrients, medium water retention
    Silt,
    /// High in organic matter, acidic
    Peat,
    /// Alkaline, often dry and stony
    Chalky,
}

/// The different types of water features
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum WaterType {
    /// Large bodies of standing water
    Lake,
    /// Moving bodies of water
    River,
    /// Large bodies of salt water
    Ocean,
    /// Shallow, stagnant water with vegetation
    Swamp,
}

/// The different biomes that can exist in the world
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum BiomeType {
    /// Forest biomes
    Forest(ForestType),
    /// Desert biomes
    Desert(DesertType),
    /// Grassland biomes
    Grassland(GrasslandType),
    /// Tundra biomes
    Tundra,
    /// Mountain biomes
    Mountain,
    /// Water biomes
    Water(WaterType),
}

impl Default for BiomeType {
    fn default() -> Self {
        BiomeType::Grassland(GrasslandType::Prairie)
    }
}

/// The different types of forests
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum ForestType {
    Temperate,  // Moderate climate, deciduous trees
    Tropical,   // Hot and humid, dense vegetation
    Boreal,     // Cold climate, coniferous trees
    Rainforest, // Very wet, extremely diverse
}

/// The different types of deserts
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum DesertType {
    Hot,   // Hot and dry, sparse vegetation
    Cold,  // Cold and dry, sparse vegetation
    Rocky, // Rocky terrain, minimal soil
    Sandy, // Sandy terrain, dunes
}

/// The different types of grasslands
#[derive(Default, Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum GrasslandType {
    /// Tropical grassland with scattered trees
    Savanna,
    /// Temperate grassland with few trees
    #[default]
    Prairie,
    /// Semi-arid grassland
    Steppe,
    /// Small grassland areas within other biomes
    Meadow,
}

/// Component that defines the terrain type of a cell
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
#[require(Transform)]
pub struct Terrain {
    /// The type of terrain
    pub terrain_type: TerrainType,
    /// The biome of this terrain cell
    pub biome: BiomeType,
    /// Properties of this terrain cell
    pub properties: TerrainProperties,
}

impl Default for Terrain {
    fn default() -> Self {
        Self {
            terrain_type: TerrainType::default(),
            biome: BiomeType::Grassland(GrasslandType::Prairie),
            properties: TerrainProperties::default(),
        }
    }
}

/// Properties of a terrain cell that affect plant growth
#[derive(Reflect, Debug, Clone)]
pub struct TerrainProperties {
    /// How much water the terrain can hold (0.0 - 1.0)
    pub water_retention: f32,
    /// How much water is currently in the terrain (0.0 - 1.0)
    pub moisture: f32,
    /// The fertility of the terrain (0.0 - 1.0)
    pub fertility: f32,
    /// The pH level of the terrain (0.0 - 14.0, 7.0 is neutral)
    pub ph: f32,
    /// The temperature of the terrain in Celsius
    pub temperature: f32,
    /// Additional properties as needed
    pub custom_properties: HashMap<String, f32>,
}

impl Default for TerrainProperties {
    fn default() -> Self {
        Self {
            water_retention: 0.5,
            moisture: 0.5,
            fertility: 0.5,
            ph: 7.0,
            temperature: 20.0,
            custom_properties: HashMap::new(),
        }
    }
}

impl TerrainProperties {
    /// Create properties for a specific soil type
    pub fn from_soil_type(soil_type: SoilType) -> Self {
        match soil_type {
            SoilType::Clay => Self {
                water_retention: 0.8,
                moisture: 0.7,
                fertility: 0.7,
                ph: 6.5,
                ..Default::default()
            },
            SoilType::Sandy => Self {
                water_retention: 0.3,
                moisture: 0.3,
                fertility: 0.3,
                ph: 6.0,
                ..Default::default()
            },
            SoilType::Loam => Self {
                water_retention: 0.6,
                moisture: 0.6,
                fertility: 0.8,
                ph: 6.8,
                ..Default::default()
            },
            SoilType::Silt => Self {
                water_retention: 0.7,
                moisture: 0.6,
                fertility: 0.6,
                ph: 6.5,
                ..Default::default()
            },
            SoilType::Peat => Self {
                water_retention: 0.9,
                moisture: 0.8,
                fertility: 0.7,
                ph: 4.5, // Acidic
                ..Default::default()
            },
            SoilType::Chalky => Self {
                water_retention: 0.4,
                moisture: 0.4,
                fertility: 0.4,
                ph: 8.5, // Alkaline
                ..Default::default()
            },
        }
    }

    /// Create properties for a water type
    pub fn from_water_type(water_type: WaterType) -> Self {
        match water_type {
            WaterType::Lake => Self {
                water_retention: 1.0,
                moisture: 1.0,
                fertility: 0.3,
                ph: 7.0,
                ..Default::default()
            },
            WaterType::River => Self {
                water_retention: 1.0,
                moisture: 1.0,
                fertility: 0.4,
                ph: 7.0,
                ..Default::default()
            },
            WaterType::Ocean => Self {
                water_retention: 1.0,
                moisture: 1.0,
                fertility: 0.1,
                ph: 8.2, // Slightly alkaline
                ..Default::default()
            },
            WaterType::Swamp => Self {
                water_retention: 1.0,
                moisture: 1.0,
                fertility: 0.7,
                ph: 5.5, // Slightly acidic
                ..Default::default()
            },
        }
    }
}

/// Component for water flow direction and speed
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
#[require(Name::new("WaterFlow"))]
pub struct WaterFlow {
    /// Direction of water flow (normalized)
    pub direction: Vec2,
    /// Speed of water flow
    pub speed: f32,
}

/// Component for terrain that can be modified (e.g., by player actions)
#[derive(Component, Debug)]
pub struct ModifiableTerrain;

/// Component for terrain that has resources that can be harvested
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
pub struct TerrainResource {
    /// The type of resource
    pub resource_type: ResourceType,
    /// The amount of resource available
    pub amount: f32,
}

/// The different types of resources that can be harvested from terrain
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum ResourceType {
    Minerals,
    OrganicMatter,
    Water,
}

/// Component for terrain that has been affected by an event (e.g., rainfall, drought)
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
pub struct TerrainEvent {
    /// The type of event
    pub event_type: TerrainEventType,
    /// The duration of the event in seconds
    pub duration: f32,
    /// The intensity of the event (0.0 - 1.0)
    pub intensity: f32,
    /// The time remaining for the event in seconds
    pub time_remaining: f32,
}

/// The different types of events that can affect terrain
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum TerrainEventType {
    Rainfall,
    Drought,
    Fire,
    Flood,
}

/// Component that represents a terrain pod (a region of the world)
/// The children contains the terrain cells
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct TerrainPod {
    /// The grid coordinates of this pod in the world
    pub grid_index: (i32, i32),
    /// The width of this pod in cells
    pub width: i32,
    /// The depth of this pod in cells
    pub depth: i32,
    /// The size of each cell in the pod
    pub cell_size: f32,
    /// The neighboring pods in each cardinal direction (North, East, South, West)
    pub neighbors: [Option<Entity>; 4],
    /// The terrain grid for this pod
    pub terrain_grid: Option<crate::resources::environment::TerrainGrid>,
}

impl TerrainPod {
    pub fn get_terrain_height(&self) -> f32 {
        self.cell_size * 10.0 + self.cell_size / 2.0
    }

    pub fn get_terrain_min_width(&self) -> f32 {
        0.
    }

    pub fn get_terrain_min_depth(&self) -> f32 {
        0.
    }

    pub fn get_terrain_max_width(&self) -> f32 {
        self.width as f32 * self.cell_size
    }

    pub fn get_terrain_max_depth(&self) -> f32 {
        self.depth as f32 * self.cell_size
    }

    pub fn is_in_bounds(&self, position: Vec3) -> bool {
        position.x >= self.get_terrain_min_width()
            && position.x < self.get_terrain_max_width()
            && position.z >= self.get_terrain_min_depth()
            && position.z < self.get_terrain_max_depth()
    }
}

/// Cardinal directions for pod connections
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum CardinalDirection {
    North,
    East,
    South,
    West,
}

impl CardinalDirection {
    /// Get the index in the neighbors array
    pub fn index(&self) -> usize {
        match self {
            CardinalDirection::North => 0,
            CardinalDirection::East => 1,
            CardinalDirection::South => 2,
            CardinalDirection::West => 3,
        }
    }

    /// Get the opposite direction
    pub fn opposite(&self) -> Self {
        match self {
            CardinalDirection::North => CardinalDirection::South,
            CardinalDirection::East => CardinalDirection::West,
            CardinalDirection::South => CardinalDirection::North,
            CardinalDirection::West => CardinalDirection::East,
        }
    }

    /// Get the grid offset for this direction
    pub fn offset(&self) -> (i32, i32) {
        match self {
            CardinalDirection::North => (0, -1),
            CardinalDirection::East => (1, 0),
            CardinalDirection::South => (0, 1),
            CardinalDirection::West => (-1, 0),
        }
    }
}

#[derive(Reflect, Default, Component, Debug)]
pub struct Sun;

#[derive(Default, Component, Debug)]
#[require(Transform)]
pub struct Rock;

/// Component for water volumes that affect physics
#[derive(Reflect, Component, Debug)]
#[reflect(Component)]
pub struct WaterVolume {
    /// Density of the water (affects buoyancy)
    pub density: f32,
}
