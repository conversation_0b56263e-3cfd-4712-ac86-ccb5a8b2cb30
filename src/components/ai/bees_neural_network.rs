use crate::components::ai::neural_network::{NeuralBrain, NeuralNetwork, SpeciesType};
use bevy::platform::collections::HashMap;
use neuroflow::data::DataSet;

impl NeuralBrain {
    pub fn new_for_bee() -> Self {
        Self::extend_for_bee()
    }

    // Extend base neural brain for bees
    pub fn extend_for_bee() -> Self {
        // Start with base neural brain
        let mut brain = Self::new_base();
        brain.species_type = SpeciesType::Bee;

        // Add bee-specific inputs
        let mut extended_inputs = HashMap::new();
        let base_input_count = brain.input_config.total_inputs;

        // Add new inputs at the end of the base inputs
        extended_inputs.insert("FlowerProximity".to_string(), base_input_count);
        extended_inputs.insert("NectarQuality".to_string(), base_input_count + 1);
        extended_inputs.insert("PollenLoad".to_string(), base_input_count + 2);

        // Add descriptions for extended inputs
        brain.input_config.descriptions.insert(
            "FlowerProximity".to_string(),
            "Distance to nearest flower (0.0=far, 1.0=close)".to_string(),
        );
        brain.input_config.descriptions.insert(
            "NectarQuality".to_string(),
            "Quality of available nectar (0.0=poor, 1.0=excellent)".to_string(),
        );
        brain.input_config.descriptions.insert(
            "PollenLoad".to_string(),
            "Current pollen load (0.0=empty, 1.0=full)".to_string(),
        );

        // Update total inputs
        brain.input_config.extended_mapping = extended_inputs;
        brain.input_config.total_inputs = base_input_count + 3;

        // Add bee-specific outputs
        let mut extended_outputs = HashMap::new();
        let base_output_count = brain.output_config.total_outputs;

        // Add new outputs at the end of the base outputs
        extended_outputs.insert("Pollinate".to_string(), base_output_count);
        extended_outputs.insert("Dance".to_string(), base_output_count + 1);
        extended_outputs.insert("ReturnToHive".to_string(), base_output_count + 2);

        // Add descriptions for extended outputs
        brain
            .output_config
            .descriptions
            .insert("Pollinate".to_string(), "Pollinate flowers".to_string());
        brain.output_config.descriptions.insert(
            "Dance".to_string(),
            "Perform waggle dance to communicate".to_string(),
        );
        brain
            .output_config
            .descriptions
            .insert("ReturnToHive".to_string(), "Return to the hive".to_string());

        // Update total outputs
        brain.output_config.extended_mapping = extended_outputs;
        brain.output_config.total_outputs = base_output_count + 3;

        // Resize input and output vectors
        brain.inputs = vec![0.5; brain.input_config.total_inputs];
        brain.outputs = vec![0.0; brain.output_config.total_outputs];

        // Create new neural network with extended layers
        let layers = vec![
            brain.input_config.total_inputs as i32,
            12,
            10,
            10,
            brain.output_config.total_outputs as i32,
        ];
        let mut network = NeuralNetwork::new(&layers);

        let folder_path = crate::components::ai::neural_network::NEURAL_NETWORK_PATH;

        // Load or initialize bee-specific training data
        let path = format!("{folder_path}/bee.flow");
        if !network.load_nn(&path) {
            // Add bee-specific training data
            Self::initialize_bee_training_data(&mut network, &brain);

            network.save_nn(&path);
        }

        brain.network = network;
        brain
    }

    // Initialize bee-specific training data
    fn initialize_bee_training_data(network: &mut NeuralNetwork, brain: &NeuralBrain) {
        // Get indices for bee-specific inputs and outputs
        let flower_idx = *brain
            .input_config
            .extended_mapping
            .get("FlowerProximity")
            .unwrap();
        let nectar_idx = *brain
            .input_config
            .extended_mapping
            .get("NectarQuality")
            .unwrap();
        let pollen_idx = *brain
            .input_config
            .extended_mapping
            .get("PollenLoad")
            .unwrap();
        let pollinate_idx = *brain
            .output_config
            .extended_mapping
            .get("Pollinate")
            .unwrap();
        let dance_idx = *brain.output_config.extended_mapping.get("Dance").unwrap();
        let return_idx = *brain
            .output_config
            .extended_mapping
            .get("ReturnToHive")
            .unwrap();

        // Create training examples for bee-specific behaviors
        let mut data_set = DataSet::new();

        // Example 1: Close to flower with low pollen load → pollinate
        let mut input_example = vec![0.0; brain.input_config.total_inputs];
        let mut output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[flower_idx] = 0.9; // Flower very close
        input_example[pollen_idx] = 0.1; // Low pollen load
        output_example[pollinate_idx] = 1.0; // Should pollinate

        data_set.push(&input_example, &output_example);

        // Example 2: High pollen load → return to hive
        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[pollen_idx] = 0.9; // High pollen load
        output_example[return_idx] = 1.0; // Should return to hive

        data_set.push(&input_example, &output_example);

        // Do not return to hive if pollen load is low
        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[pollen_idx] = 0.1; // Low pollen load
        output_example[return_idx] = 0.0; // Should not return to hive

        data_set.push(&input_example, &output_example);

        // Do not pollinate if pollen load is high
        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[pollen_idx] = 0.9; // High pollen load
        output_example[pollinate_idx] = 0.0; // Should not pollinate

        data_set.push(&input_example, &output_example);

        // Example 3: Returned to hive with high pollen load → dance
        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[pollen_idx] = 0.9; // High pollen load
        input_example[nectar_idx] = 0.8; // Good nectar quality
        output_example[dance_idx] = 1.0; // Should dance

        data_set.push(&input_example, &output_example);

        // Pollination is high priority. Add more examples
        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[flower_idx] = 0.9; // Flower very close
        input_example[pollen_idx] = 0.1; // Low pollen load
        output_example[pollinate_idx] = 1.0; // Should pollinate

        data_set.push(&input_example, &output_example);

        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[flower_idx] = 0.1;
        input_example[pollen_idx] = 0.5;
        output_example[pollinate_idx] = 1.0; // Should pollinate

        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[flower_idx] = 0.5;
        input_example[pollen_idx] = 0.5;
        output_example[pollinate_idx] = 1.0; // Should pollinate

        input_example = vec![0.0; brain.input_config.total_inputs];
        output_example = vec![0.0; brain.output_config.total_outputs];

        input_example[flower_idx] = 0.3;
        input_example[pollen_idx] = 0.7;
        output_example[pollinate_idx] = 1.0; // Should pollinate

        data_set.push(&input_example, &output_example);

        // Train the network with bee-specific data
        network.train(&data_set, 20_000);
    }
}
