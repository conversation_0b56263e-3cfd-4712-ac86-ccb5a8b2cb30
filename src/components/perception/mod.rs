use crate::components::ecosystem::FoodType;
use bevy::ecs::component::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>x<PERSON>, <PERSON><PERSON>, StorageType};
use bevy::ecs::world::DeferredWorld;
use bevy::prelude::*;
use std::collections::{HashMap, VecDeque};
use std::time::Duration;

/// Main component for AI perception capabilities
#[derive(<PERSON>fle<PERSON>, Compo<PERSON>, Debug, <PERSON><PERSON>)]
#[reflect(<PERSON><PERSON><PERSON>, Debug, <PERSON>lone)]
#[component(on_add = on_ai_perception_add)]
pub struct AIPerception {
    /// Active senses for this entity
    pub active_senses: Vec<SenseType>,
    /// Last time each stimulus was detected
    #[reflect(ignore)]
    pub last_detection_time: HashMap<StimulusType, f32>,
    /// Currently detected stimuli (short-term memory)
    #[reflect(ignore)]
    pub detected_stimuli: Vec<DetectedStimulus>,
    /// Maximum age of stimuli to keep in short-term memory (in seconds)
    pub short_term_memory_duration: f32,
    /// Long-term memory storage for important stimuli
    pub long_term_memory: Vec<LongTermMemoryItem>,
    /// Maximum age of items to keep in long-term memory (in seconds)
    /// Set to a very large value for permanent storage
    pub long_term_memory_duration: f32,
    /// Minimum importance threshold for stimuli to be stored in long-term memory (0.0 - 1.0)
    pub memory_importance_threshold: f32,
}

impl AIPerception {
    /// Get an iterator over detected visual stimuli in short-term memory
    pub fn detected_visual_stimuli_iter(
        &self,
    ) -> impl Iterator<Item = &DetectedStimulus> {
        self.detected_stimuli.iter().filter(|s| {
            s.stimulus_type == StimulusType::Visual && s.source_entity.is_some()
        })
    }

    /// Get an iterator over detected smell stimuli in short-term memory
    pub fn detected_smell_stimuli_iter(&self) -> impl Iterator<Item = &DetectedStimulus> {
        self.detected_stimuli.iter().filter(|s| {
            s.stimulus_type == StimulusType::Smell && s.source_entity.is_some()
        })
    }

    /// Get an iterator over all long-term memory items
    pub fn long_term_memory_iter(&self) -> impl Iterator<Item = &LongTermMemoryItem> {
        self.long_term_memory.iter()
    }

    /// Get an iterator over long-term memory items of a specific category
    pub fn long_term_memory_by_category_iter(
        &self,
        category: MemoryCategory,
    ) -> impl Iterator<Item = &LongTermMemoryItem> {
        self.long_term_memory
            .iter()
            .filter(move |m| m.category == category)
    }

    /// Get an iterator over long-term memory items related to a specific stimulus type
    pub fn long_term_memory_by_stimulus_type_iter(
        &self,
        stimulus_type: StimulusType,
    ) -> impl Iterator<Item = &LongTermMemoryItem> {
        self.long_term_memory
            .iter()
            .filter(move |m| m.stimulus.stimulus_type == stimulus_type)
    }

    /// Get an iterator over long-term memory items related to a specific entity
    pub fn long_term_memory_by_entity_iter(
        &self,
        entity: Entity,
    ) -> impl Iterator<Item = &LongTermMemoryItem> {
        self.long_term_memory.iter().filter(move |m| {
            m.stimulus.source_entity.is_some()
                && m.stimulus.source_entity.unwrap() == entity
        })
    }

    /// Add a new item to long-term memory or update an existing one
    pub fn add_to_long_term_memory(
        &mut self,
        stimulus: &DetectedStimulus,
        importance: f32,
        category: MemoryCategory,
        current_time: f32,
        notes: Option<String>,
    ) {
        // Check if we already have a memory for this stimulus source
        if let Some(source_entity) = stimulus.source_entity {
            // Try to find an existing memory for this entity
            if let Some(existing_memory) = self.long_term_memory.iter_mut().find(|m| {
                m.stimulus.source_entity.is_some()
                    && m.stimulus.source_entity.unwrap() == source_entity
                    && m.stimulus.stimulus_type == stimulus.stimulus_type
            }) {
                // Update the existing memory
                existing_memory.last_encounter_time = current_time;
                existing_memory.encounter_count += 1;

                // Update importance if the new stimulus is more important
                if importance > existing_memory.importance {
                    existing_memory.importance = importance;
                }

                // Update notes if provided
                if let Some(new_notes) = notes {
                    existing_memory.notes = Some(new_notes);
                }

                // Update the stimulus data with the most recent one
                existing_memory.stimulus = stimulus.clone();

                return;
            }
        }

        // If we didn't find an existing memory or there's no source entity, create a new one
        let memory_item = LongTermMemoryItem {
            stimulus: stimulus.clone(),
            importance,
            first_encounter_time: current_time,
            last_encounter_time: current_time,
            encounter_count: 1,
            category,
            notes,
        };

        self.long_term_memory.push(memory_item);
    }

    /// Remove a specific memory item from long-term memory
    pub fn remove_from_long_term_memory(
        &mut self,
        index: usize,
    ) -> Option<LongTermMemoryItem> {
        if index < self.long_term_memory.len() {
            Some(self.long_term_memory.remove(index))
        } else {
            None
        }
    }

    /// Clear all long-term memories
    pub fn clear_long_term_memory(&mut self) {
        self.long_term_memory.clear();
    }

    /// Find the most important memory in a specific category
    pub fn most_important_memory_in_category(
        &self,
        category: MemoryCategory,
    ) -> Option<&LongTermMemoryItem> {
        self.long_term_memory_by_category_iter(category)
            .max_by(|a, b| a.importance.partial_cmp(&b.importance).unwrap())
    }

    /// Find the most recently encountered memory
    pub fn most_recent_memory(&self) -> Option<&LongTermMemoryItem> {
        self.long_term_memory.iter().max_by(|a, b| {
            a.last_encounter_time
                .partial_cmp(&b.last_encounter_time)
                .unwrap()
        })
    }
}

impl Default for AIPerception {
    fn default() -> Self {
        Self {
            active_senses: vec![],
            last_detection_time: HashMap::new(),
            detected_stimuli: Vec::new(),
            short_term_memory_duration: 3.0,
            long_term_memory: Vec::new(),
            long_term_memory_duration: 300.0, // 5 minutes by default
            memory_importance_threshold: 0.7, // Only store important memories
        }
    }
}

fn on_ai_perception_add(
    mut world: DeferredWorld,
    HookContext { entity, .. }: HookContext,
) {
    // Check what senses the AI Perception component has and add the
    // appropriate sense components if they don't already exist
    let Some(ai_perception) = world.entity(entity).get::<AIPerception>() else {
        return;
    };

    let senses = ai_perception.active_senses.clone();

    for sense in senses.iter() {
        match sense {
            SenseType::Sight => {
                if !world.entity(entity).contains::<SightPerception>() {
                    world.commands().entity(entity).insert(SightPerception::default());
                }
            }
            SenseType::Hearing => {
                if !world.entity(entity).contains::<HearingPerception>() {
                    world.commands().entity(entity).insert(HearingPerception::default());
                }
            }
            SenseType::Touch => {
                if !world.entity(entity).contains::<TouchPerception>() {
                    world.commands().entity(entity).insert(TouchPerception::default());
                }
            }
            SenseType::Damage => {
                if !world.entity(entity).contains::<DamagePerception>() {
                    world.commands().entity(entity).insert(DamagePerception::default());
                }
            }
            SenseType::Smell => {
                if !world.entity(entity).contains::<SmellPerception>() {
                    world.commands().entity(entity).insert(SmellPerception::default());
                }
            }
            SenseType::Prediction => {
                // No component for prediction, as it's handled by the AI system itself
            }
        }
    }
}

/// Types of senses an organism can have
#[derive(Reflect, Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum SenseType {
    Sight,
    Hearing,
    Touch,
    Damage,
    Smell,
    Prediction,
}

impl SenseType {
    pub fn get_all() -> Vec<SenseType> {
        vec![
            SenseType::Sight,
            SenseType::Hearing,
            SenseType::Touch,
            SenseType::Damage,
            SenseType::Smell,
            SenseType::Prediction,
        ]
    }
}

/// Types of stimuli that can be detected
#[derive(Reflect, Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum StimulusType {
    Visual,
    Sound,
    Touch,
    Damage,
    Smell,
    Movement,
}

/// A detected stimulus with relevant information
#[derive(Reflect, Debug, Clone)]
pub struct DetectedStimulus {
    /// The type of stimulus
    pub stimulus_type: StimulusType,
    /// The entity that caused the stimulus
    pub source_entity: Option<Entity>,
    /// The location of the stimulus in world space
    pub location: Vec3,
    /// The strength of the stimulus (0.0 - 1.0)
    pub strength: f32,
    /// When the stimulus was detected (simulation time)
    pub detection_time: f32,
    /// Additional data specific to the stimulus type
    pub data: StimulusData,
}

/// Categories for long-term memory items
#[derive(Reflect, Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum MemoryCategory {
    /// Important locations (water sources, food, shelter, etc.)
    Location,
    /// Other organisms or entities
    Entity,
    /// Dangerous or threatening stimuli
    Threat,
    /// Resources or beneficial stimuli
    Resource,
    /// General category for other memories
    General,
}

/// An item stored in long-term memory
#[derive(Reflect, Debug, Clone)]
pub struct LongTermMemoryItem {
    /// The original stimulus that created this memory
    pub stimulus: DetectedStimulus,
    /// The importance of this memory (0.0 - 1.0)
    pub importance: f32,
    /// When this memory was first created (simulation time)
    pub first_encounter_time: f32,
    /// When this memory was last reinforced (simulation time)
    pub last_encounter_time: f32,
    /// How many times this memory has been reinforced
    pub encounter_count: u32,
    /// The category of this memory
    pub category: MemoryCategory,
    /// Optional description or notes about this memory
    pub notes: Option<String>,
}

/// Additional data for specific stimulus types
#[derive(Reflect, Debug, Clone)]
pub enum StimulusData {
    /// No additional data
    None,
    /// Damage amount and type
    Damage { amount: f32, damage_type: String },
    /// Sound data
    Sound { volume: f32, frequency: f32 },
    /// Visual data
    Visual { color: Color, size: f32 },
    /// Movement data
    Movement { velocity: Vec3, speed: f32 },
    /// Food data
    Food {
        /// Nutritional value when consumed (how much it increases hunger)
        nutrition_value: f32,
        /// Type of food (e.g., "plant", "meat", "nectar")
        food_type: FoodType,
        /// Whether this food source is consumable
        is_consumable: bool,
    },
}

/// Component for sight perception
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct SightPerception {
    /// Field of view in degrees
    pub fov: f32,
    /// Maximum distance for sight
    pub sight_radius: f32,
    /// Radius within which the entity can lose sight of a stimulus
    pub lose_sight_radius: f32,
    /// Peripheral vision effectiveness (0.0 - 1.0)
    pub peripheral_vision: f32,
    /// Whether this sense is currently active
    pub active: bool,
}

impl Default for SightPerception {
    fn default() -> Self {
        Self {
            fov: 120.0,
            sight_radius: 1.0,
            lose_sight_radius: 1.5,
            peripheral_vision: 0.5,
            active: true,
        }
    }
}

/// Component for hearing perception
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct HearingPerception {
    /// Maximum distance for hearing
    pub hearing_range: f32,
    /// Sensitivity to sounds (0.0 - 1.0)
    pub sensitivity: f32,
    /// Whether this sense is currently active
    pub active: bool,
}

impl Default for HearingPerception {
    fn default() -> Self {
        Self {
            hearing_range: 15.0,
            sensitivity: 0.8,
            active: true,
        }
    }
}

/// Component for touch perception
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct TouchPerception {
    /// Sensitivity to touch (0.0 - 1.0)
    pub sensitivity: f32,
    /// Whether this sense is currently active
    pub active: bool,
}

impl Default for TouchPerception {
    fn default() -> Self {
        Self {
            sensitivity: 1.0,
            active: true,
        }
    }
}

/// Component for damage perception
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct DamagePerception {
    /// Sensitivity to damage (0.0 - 1.0)
    pub sensitivity: f32,
    /// Whether this sense is currently active
    pub active: bool,
}

impl Default for DamagePerception {
    fn default() -> Self {
        Self {
            sensitivity: 1.0,
            active: true,
        }
    }
}

/// Component for smell perception
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct SmellPerception {
    /// Maximum distance for smell
    pub smell_range: f32,
    /// Sensitivity to smells (0.0 - 1.0)
    pub sensitivity: f32,
    /// Whether this sense is currently active
    pub active: bool,
}

impl Default for SmellPerception {
    fn default() -> Self {
        Self {
            smell_range: 10.0,
            sensitivity: 0.7,
            active: true,
        }
    }
}

/// Component for an entity that produces a stimulus
#[derive(Reflect, Debug, Clone)]
pub struct StimulusSource {
    /// The type of stimulus this entity produces
    pub stimulus_type: StimulusType,
    /// The strength of the stimulus (0.0 - 1.0)
    pub strength: f32,
    /// Whether the stimulus is currently active
    pub active: bool,
    /// Additional data for the stimulus
    pub data: StimulusData,
}

impl StimulusSource {
    pub fn new(stimulus_type: StimulusType, strength: f32) -> Self {
        Self {
            stimulus_type,
            strength,
            active: true,
            data: StimulusData::None,
        }
    }

    pub fn with_data(mut self, data: StimulusData) -> Self {
        self.data = data;
        self
    }
}

/// Component that allows an entity to have multiple stimulus sources
#[derive(Reflect, Component, Debug, Clone, Default)]
#[reflect(Component)]
pub struct StimulusSources {
    /// The collection of stimulus sources this entity produces
    pub sources: Vec<StimulusSource>,
}

impl StimulusSources {
    /// Create a new empty StimulusSources component
    pub fn new() -> Self {
        Self {
            sources: Vec::new(),
        }
    }

    /// Create a StimulusSources component with initial sources
    pub fn with_sources(sources: Vec<StimulusSource>) -> Self {
        Self { sources }
    }

    /// Add a stimulus source to the collection
    pub fn add(&mut self, source: StimulusSource) {
        self.sources.push(source);
    }

    /// Remove a stimulus source by index
    pub fn remove(&mut self, index: usize) -> Option<StimulusSource> {
        if index < self.sources.len() {
            Some(self.sources.remove(index))
        } else {
            None
        }
    }

    /// Get a stimulus source by index
    pub fn get(&self, index: usize) -> Option<&StimulusSource> {
        self.sources.get(index)
    }

    /// Get a mutable reference to a stimulus source by index
    pub fn get_mut(&mut self, index: usize) -> Option<&mut StimulusSource> {
        self.sources.get_mut(index)
    }

    /// Get an iterator over all stimulus sources
    pub fn iter(&self) -> impl Iterator<Item = &StimulusSource> {
        self.sources.iter()
    }

    /// Get a mutable iterator over all stimulus sources
    pub fn iter_mut(&mut self) -> impl Iterator<Item = &mut StimulusSource> {
        self.sources.iter_mut()
    }
}

/// Event sent when a stimulus is detected
#[derive(Event, Debug, Clone)]
pub struct StimulusDetectedEvent {
    /// The entity that detected the stimulus
    pub detector_entity: Entity,
    /// The detected stimulus
    pub stimulus: DetectedStimulus,
}

/// Event sent when a stimulus is forgotten (expired from memory)
#[derive(Event, Debug, Clone)]
pub struct StimulusForgottenEvent {
    /// The entity that forgot the stimulus
    pub detector_entity: Entity,
    /// The type of stimulus that was forgotten
    pub stimulus_type: StimulusType,
    /// The entity that was the source of the stimulus
    pub source_entity: Option<Entity>,
}

/// Event sent to broadcast a stimulus to nearby entities
#[derive(Event, Debug, Clone)]
pub struct BroadcastStimulusEvent {
    /// The entity that is the source of the stimulus
    pub source_entity: Entity,
    /// The type of stimulus
    pub stimulus_type: StimulusType,
    /// The location of the stimulus in world space
    pub location: Vec3,
    /// The strength of the stimulus (0.0 - 1.0)
    pub strength: f32,
    /// The maximum range of the stimulus
    pub range: f32,
    /// Additional data for the stimulus
    pub data: StimulusData,
}

#[derive(Component, Debug, Clone)]
pub struct ShowDebugPerceptionGizmos;

/// Component to store the detected stimulus for behavior trees
#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct DetectedStimulusTarget {
    /// The detected stimulus
    pub stimulus: DetectedStimulus,
}
