use bevy::prelude::*;

/// Component that tracks a plant's ability to absorb light for photosynthesis
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct LightAbsorption {
    /// The current amount of light being absorbed (0.0 - 1.0)
    pub current_absorption: f32,
    /// The accumulated energy from photosynthesis
    pub energy_stored: f32,
    /// Maximum energy storage capacity
    pub max_energy: f32,
    /// How efficiently the plant converts light to energy
    pub efficiency: f32,
}

impl Default for LightAbsorption {
    fn default() -> Self {
        Self {
            current_absorption: 0.0,
            energy_stored: 10.0, // Start with some energy
            max_energy: 100.0,
            efficiency: 1.0,
        }
    }
}

/// Component that represents the energy consumption rate of a plant
#[derive(Component, Reflect, serde::Deserialize, Debug, Clone)]
#[reflect(Component)]
#[require(Name::new("EnergyConsumption"))]
pub struct EnergyConsumption {
    /// Base rate of energy consumption per second
    #[serde(default = "default_base_rate")]
    pub base_rate: f32,
    /// Additional energy consumption during growth
    #[serde(default = "default_growth_rate")]
    pub growth_rate: f32,
    /// Additional energy consumption during reproduction
    #[serde(default = "default_reproduction_rate")]
    pub reproduction_rate: f32,
    /// Net energy change in the last update (production - consumption)
    #[serde(default, skip)]
    pub net_energy_change: f32,
}

impl EnergyConsumption {
    pub fn reset(&mut self) -> Self {
        self.net_energy_change = 0.0;
        self.clone()
    }

    pub fn get_reproduction_rate(&self) -> f32 {
        self.reproduction_rate.clamp(0.0, 1.0)
    }
}

impl Default for EnergyConsumption {
    fn default() -> Self {
        Self {
            base_rate: 0.1,
            growth_rate: 0.5,
            reproduction_rate: 0.1,
            net_energy_change: 0.0,
        }
    }
}

fn default_base_rate() -> f32 {
    EnergyConsumption::default().base_rate
}

fn default_growth_rate() -> f32 {
    EnergyConsumption::default().growth_rate
}

fn default_reproduction_rate() -> f32 {
    EnergyConsumption::default().reproduction_rate
}
