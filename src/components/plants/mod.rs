use crate::components::ecosystem::*;
use crate::components::genetics::Fitness;
use crate::components::lifecycle::{Lifespan, Organism};
use crate::components::plants::lifecycle::PlantLifecycle;
use crate::components::plants::photosynthesis::{EnergyConsumption, LightAbsorption};
use crate::components::GameID;
use crate::resources::plants::PlantSpeciesData;
use bevy::prelude::*;

pub mod lifecycle;
pub mod photosynthesis;
pub mod pollination;
pub mod render;

/// Defines the type of pollination mechanism a plant uses
#[derive(serde::Deserialize, Reflect, Component, Debug, Clone, Copy, PartialEq, Eq)]
#[reflect(Component)]
pub enum PlantPollinationType {
    /// Animal pollinated plants that produce nectar
    AnimalPollinated,
    /// Wind pollinated plants that don't produce nectar
    WindPollinated,
}

impl Default for PlantPollinationType {
    fn default() -> Self {
        Self::WindPollinated
    }
}

/// Component for plants that can produce nectar (animal pollinated plants)
#[derive(serde::Deserialize, Reflect, Component, Debug, Clone)]
#[reflect(Component)]
pub struct NectarProduction {
    /// Current amount of nectar
    #[serde(default)]
    pub current_nectar: f32,
    /// Maximum nectar capacity
    #[serde(default = "default_max_nectar")]
    pub max_nectar: f32,
    /// Rate of nectar production per second
    #[serde(default = "default_production_rate")]
    pub production_rate: f32,
    /// Whether the plant is currently producing nectar
    #[serde(default)]
    pub is_producing: bool,
}

fn default_max_nectar() -> f32 {
    NectarProduction::default().max_nectar
}

fn default_production_rate() -> f32 {
    NectarProduction::default().production_rate
}

impl Default for NectarProduction {
    fn default() -> Self {
        Self {
            current_nectar: 0.0,
            max_nectar: 1.0,
            production_rate: 0.1,
            is_producing: false,
        }
    }
}

#[derive(Component, Clone)]
#[require(
    Producer,
    Fitness,
    GlobalTransform,
    Transform,
    GameID,
    PlantAge,
    PlantGrowthStage,
    EnergyConsumption,
    LightAbsorption,
    PlantPollinationType,
    InheritedVisibility,
    Lifespan,
    Organism,
    PlantLifecycle
)]
pub struct Plant;

#[derive(Component)]
pub struct Flower;

/// Marker component for plants that can be pollinated by animals
#[derive(Component)]
pub struct AnimalPollinatedPlant;

/// Marker component for plants that are pollinated by wind
#[derive(Component)]
pub struct WindPollinatedPlant;

#[derive(Component, Clone)]
pub struct PlantTree;

#[derive(Default, Component, Reflect)]
#[reflect(Component)]
#[require(Name::new("PlantAge"))]
pub struct PlantAge(pub f32);

#[derive(Reflect, Component, Debug, Default, PartialEq, Eq, Clone, Copy)]
#[reflect(Component)]
pub enum PlantGrowthStage {
    #[default]
    Seed,
    Sprout,
    Mature,
}

#[derive(Component)]
pub struct PlantMatured;

#[derive(Component, Clone)]
pub struct PlantWaste;

#[derive(Reflect, Component, Clone)]
pub struct PlantSpeciesName(pub String);

#[derive(Reflect, Component, Clone)]
pub struct PlantName(pub String);

#[derive(Reflect, Component, Clone)]
#[reflect(Component)]
pub struct PlantProperties(pub PlantSpeciesData);
