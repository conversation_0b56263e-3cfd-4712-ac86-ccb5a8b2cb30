use bevy::prelude::*;

/// Component for physical pollen particles in the world
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct PhysicalPollen {
    /// Source plant entity
    pub source_plant: Entity,
    /// Genetic material from source plant
    pub source_genome: crate::components::genetics::Genome,
    /// Viability of the pollen (decreases over time)
    pub viability: f32,
    /// Lifetime of the pollen in seconds
    pub lifetime: f32,
    /// Maximum lifetime of the pollen
    pub max_lifetime: f32,
    /// Mass of the pollen (affects physics)
    pub mass: f32,
    /// Drag coefficient (affects how much wind affects it)
    pub drag: f32,
}

impl Default for PhysicalPollen {
    fn default() -> Self {
        Self {
            source_plant: Entity::PLACEHOLDER,
            source_genome: crate::components::genetics::Genome::default(),
            viability: 1.0,
            lifetime: 0.0,
            max_lifetime: 60.0, // 1 minute default lifetime
            mass: 0.01,         // Very light
            drag: 0.8,          // High drag coefficient
        }
    }
}

/// Component that tracks a plant's pollination status
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct PollinationStatus {
    /// Whether the plant is ready to be pollinated
    pub ready_for_pollination: bool,
    /// Whether the plant has been pollinated
    pub is_pollinated: bool,
    /// Time since last pollination attempt
    pub time_since_pollination: f32,
    /// Pollen strength (affects success chance)
    pub pollen_strength: f32,
}

impl PollinationStatus {
    pub fn reset(&mut self) -> Self {
        self.is_pollinated = false;
        self.time_since_pollination = 0.0;
        self.clone()
    }
}

impl Default for PollinationStatus {
    fn default() -> Self {
        Self {
            ready_for_pollination: false,
            is_pollinated: false,
            time_since_pollination: 0.0,
            pollen_strength: 1.0,
        }
    }
}

/// Component that represents pollen carried by pollinators
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
#[component(storage = "SparseSet")]
pub struct Pollen {
    /// Source plant entity
    pub source_plant: Entity,
    /// Genetic material from source plant
    pub source_genome: crate::components::genetics::Genome,
    /// Pollen viability (decreases over time)
    pub viability: f32,
}

#[derive(Component)]
#[component(storage = "SparseSet")]
pub struct Pollinated;
