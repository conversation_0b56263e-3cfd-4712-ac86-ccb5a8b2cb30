use crate::components::environment::{
    CardinalD<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>in<PERSON>ell, TerrainPod, TerrainType
};
use crate::resources::plants::{PlantSpeciesAsset, SpriteKey};
use bevy::platform::collections::HashMap;
use bevy::prelude::*;
use bevy_asset_loader::asset_collection::AssetCollection;

/// Resource that stores the terrain grid (for a single pod)
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
#[require(Name::new("TerrainGrid"))]
pub struct TerrainGrid {
    /// The width of the terrain grid
    pub width: i32,
    /// The depth of the terrain grid
    pub depth: i32,
    /// The size of each cell in the grid
    pub cell_size: f32,
    /// Map of grid coordinates to terrain entities
    pub cells: HashMap<(i32, i32), Entity>,
    /// The pod this grid belongs to
    pub pod_index: (i32, i32),
}

impl Default for TerrainGrid {
    fn default() -> Self {
        Self {
            width: 50,
            depth: 50,
            cell_size: 1.0,
            cells: HashMap::new(),
            pod_index: (0, 0),
        }
    }
}

impl TerrainGrid {
    /// Create a new terrain grid with the given dimensions
    pub fn new(width: i32, depth: i32, cell_size: f32) -> Self {
        Self {
            width,
            depth,
            cell_size,
            cells: HashMap::new(),
            pod_index: (0, 0),
        }
    }

    /// Create a new terrain grid for a specific pod
    pub fn new_for_pod(
        width: i32,
        depth: i32,
        cell_size: f32,
        pod_index: (i32, i32),
    ) -> Self {
        Self {
            width,
            depth,
            cell_size,
            cells: HashMap::new(),
            pod_index,
        }
    }

    /// Get the entity at the given grid coordinates
    pub fn get_cell(&self, x: i32, z: i32) -> Option<&Entity> {
        self.cells.get(&(x, z))
    }

    /// Get the world position of the given grid coordinates within this pod
    pub fn grid_to_world(&self, x: i32, z: i32) -> Vec3 {
        // Calculate the base position of this pod
        let pod_x = self.pod_index.0 as f32 * (self.width as f32 * self.cell_size);
        let pod_z = self.pod_index.1 as f32 * (self.depth as f32 * self.cell_size);

        Vec3::new(
            pod_x + x as f32 * self.cell_size,
            0.0, // Y is determined by elevation
            pod_z + z as f32 * self.cell_size,
        )
    }

    /// Get the grid coordinates of the given world position within this pod
    pub fn world_to_grid(&self, position: Vec3) -> (i32, i32) {
        // Calculate the base position of this pod
        let pod_x = self.pod_index.0 as f32 * (self.width as f32 * self.cell_size);
        let pod_z = self.pod_index.1 as f32 * (self.depth as f32 * self.cell_size);

        // Adjust the position relative to the pod
        let relative_x = position.x - pod_x;
        let relative_z = position.z - pod_z;

        (
            (relative_x / self.cell_size).floor() as i32,
            (relative_z / self.cell_size).floor() as i32,
        )
    }

    /// Check if the given grid coordinates are within the grid bounds
    pub fn is_in_bounds(&self, x: i32, z: i32) -> bool {
        x >= 0 && x < self.width && z >= 0 && z < self.depth
    }

    /// Get the neighbors of the given grid coordinates
    pub fn get_neighbors(&self, x: i32, z: i32) -> Vec<(i32, i32)> {
        let mut neighbors = Vec::new();

        // Check all 8 surrounding cells
        for dx in -1..=1 {
            for dz in -1..=1 {
                if dx == 0 && dz == 0 {
                    continue; // Skip the center cell
                }

                let nx = x + dx;
                let nz = z + dz;

                if self.is_in_bounds(nx, nz) {
                    neighbors.push((nx, nz));
                }
            }
        }

        neighbors
    }

    /// Get the 4 adjacent neighbors (not diagonals)
    pub fn get_adjacent_neighbors(&self, x: i32, z: i32) -> Vec<(i32, i32)> {
        let mut neighbors = Vec::new();

        // Check the 4 adjacent cells (not diagonals)
        for (dx, dz) in [(0, 1), (1, 0), (0, -1), (-1, 0)] {
            let nx = x + dx;
            let nz = z + dz;

            if self.is_in_bounds(nx, nz) {
                neighbors.push((nx, nz));
            }
        }

        neighbors
    }
}

/// Resource that stores all terrain pods in the world
#[derive(Resource, Reflect)]
#[reflect(Resource)]
pub struct TerrainPodGrid {
    /// Map of pod grid coordinates to pod entities
    pub pods: HashMap<(i32, i32), Entity>,
    /// The size of each pod in cells
    pub pod_size: (i32, i32),
    /// The size of each cell in the grid
    pub cell_size: f32,
    /// The current active pod
    pub active_pod: Option<(i32, i32)>,
}

impl Default for TerrainPodGrid {
    fn default() -> Self {
        Self {
            pods: HashMap::new(),
            pod_size: (50, 50),
            cell_size: 1.0,
            active_pod: Some((0, 0)),
        }
    }
}

impl TerrainPodGrid {
    /// Create a new terrain pod grid
    pub fn new(pod_size: (i32, i32), cell_size: f32) -> Self {
        Self {
            pods: HashMap::new(),
            pod_size,
            cell_size,
            ..default()
        }
    }

    /// Get the height of the terrain in the pod grid
    pub fn get_terrain_min_height(&self) -> f32 {
        0.25
    }

    pub fn get_terrain_max_height(&self) -> f32 {
        5.0
    }

    pub fn get_terrain_min_width(&self) -> f32 {
        0.
    }

    pub fn get_terrain_min_depth(&self) -> f32 {
        0.
    }

    pub fn get_terrain_max_width(&self) -> f32 {
        self.pod_size.0 as f32 * self.cell_size
    }

    pub fn get_terrain_max_depth(&self) -> f32 {
        self.pod_size.1 as f32 * self.cell_size
    }

    /// Get the pod entity at the given grid coordinates
    pub fn get_pod(&self, grid_x: i32, grid_z: i32) -> Option<&Entity> {
        self.pods.get(&(grid_x, grid_z))
    }

    /// Add a pod to the grid
    pub fn add_pod(&mut self, grid_x: i32, grid_z: i32, entity: Entity) {
        self.pods.insert((grid_x, grid_z), entity);
    }

    /// Set the active pod
    pub fn set_active_pod(&mut self, grid_x: i32, grid_z: i32) {
        self.active_pod = Some((grid_x, grid_z));
    }

    /// Get the world position of a cell
    pub fn cell_to_world(&self, grid_x: i32, grid_z: i32) -> Vec3 {
        Vec3::new(
            grid_x as f32 * (self.get_terrain_max_width()),
            0.0,
            grid_z as f32 * (self.get_terrain_max_depth()),
        )
    }

    /// Get the cell coordinates from a world position
    pub fn world_to_cell(&self, position: Vec3) -> (i32, i32) {
        let pod_width = self.get_terrain_max_width();
        let pod_depth = self.get_terrain_max_depth();

        ((position.x / pod_width).floor() as i32, (position.z / pod_depth).floor() as i32)
    }

    /// Get the neighboring pod in the given direction
    pub fn get_neighbor(
        &self,
        grid_x: i32,
        grid_z: i32,
        direction: CardinalDirection,
    ) -> Option<&Entity> {
        let (dx, dz) = direction.offset();
        self.get_pod(grid_x + dx, grid_z + dz)
    }

    pub fn is_in_bounds(&self, position: Vec3) -> bool {
        let pod_width = self.get_terrain_max_width();
        let pod_depth = self.get_terrain_max_depth();

        position.x >= 0.0
            && position.x < pod_width
            && position.z >= 0.0
            && position.z < pod_depth
    }
}

/// Resource that stores climate information
#[derive(Resource, Reflect)]
#[reflect(Resource)]
pub struct Climate {
    /// The base temperature in Celsius
    pub base_temperature: f32,
    /// The current rainfall level (0.0 - 1.0)
    pub rainfall: f32,
    /// The current humidity level (0.0 - 1.0)
    pub humidity: f32,
    /// The current wind speed
    pub wind_speed: f32,
    /// The current wind direction (normalized)
    pub wind_direction: Vec2,
    /// The current season
    pub season: Season,
}

impl Default for Climate {
    fn default() -> Self {
        Self {
            base_temperature: 25.0,
            rainfall: 0.0,
            humidity: 0.1,
            wind_speed: 0.1,
            wind_direction: Vec2::new(1.0, 0.0),
            season: Season::default(),
        }
    }
}

/// The different seasons in the game
#[derive(Default, Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum Season {
    #[default]
    Spring,
    Summer,
    Autumn,
    Winter,
}

/// Resource that stores water flow information
#[derive(Resource)]
pub struct WaterSystem {
    /// Map of water source entities to their flow rate
    pub sources: HashMap<Entity, f32>,
    /// Map of water sink entities to their capacity
    pub sinks: HashMap<Entity, f32>,
    /// The global water table level (affects underground water)
    pub water_table: f32,
}

impl Default for WaterSystem {
    fn default() -> Self {
        Self {
            sources: HashMap::new(),
            sinks: HashMap::new(),
            water_table: 0.5,
        }
    }
}

/// Resource that stores environment events
#[derive(Resource, Reflect)]
#[reflect(Resource)]
pub struct EnvironmentEvents {
    /// The active global events
    pub active_events: Vec<GlobalEnvironmentEvent>,
    /// Map of local events by location
    pub local_events: HashMap<(i32, i32), Vec<LocalEnvironmentEvent>>,
}

impl Default for EnvironmentEvents {
    fn default() -> Self {
        Self {
            active_events: Vec::new(),
            local_events: HashMap::new(),
        }
    }
}

/// A global environment event that affects the entire map
#[derive(Reflect, Clone, Debug)]
pub struct GlobalEnvironmentEvent {
    /// The type of event
    pub event_type: GlobalEventType,
    /// The duration of the event in seconds
    pub duration: f32,
    /// The intensity of the event (0.0 - 1.0)
    pub intensity: f32,
    /// The time remaining for the event in seconds
    pub time_remaining: f32,
}

/// The different types of global events
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum GlobalEventType {
    /// A period of heavy rainfall
    Rainfall,
    /// A lack of rainfall
    Drought,
    /// A period of high temperatures
    HeatWave,
    /// A period of low temperatures
    ColdSnap,
    /// A severe weather event
    Storm,
    /// A period of heavy snowfall
    Snowfall,
}

/// A local environment event that affects a specific area
#[derive(Reflect, Clone, Debug)]
pub struct LocalEnvironmentEvent {
    /// The type of event
    pub event_type: LocalEventType,
    /// The duration of the event in seconds
    pub duration: f32,
    /// The intensity of the event (0.0 - 1.0)
    pub intensity: f32,
    /// The time remaining for the event in seconds
    pub time_remaining: f32,
    /// The radius of effect in grid cells
    pub radius: i32,
}

/// The different types of local events
#[derive(Reflect, Clone, Copy, Debug, PartialEq, Eq, Hash)]
pub enum LocalEventType {
    Fire,
    Flood,
    Landslide,
    Earthquake,
    Eruption,
}

#[derive(AssetCollection, Resource, Debug)]
pub struct EnvironmentAssets {
    #[asset(path = "models/nature_stylized", collection(mapped, typed))]
    pub nature_stylized: HashMap<String, Handle<Gltf>>,
    // #[asset(path = "models/nature_crops", collection(mapped, typed))]
    // pub nature_crops: HashMap<String, Handle<Mesh>>,
}

#[allow(non_snake_case, non_upper_case_globals)]
pub mod EnvironmentAssetsKeys {
    pub const ROCK_1: &'static str = "Rock_1";
    pub const ROCK_2: &'static str = "Rock_2";
    pub const ROCK_3: &'static str = "Rock_3";
    pub const ROCK_4: &'static str = "Rock_4";
    pub const ROCK_5: &'static str = "Rock_5";
}

impl EnvironmentAssets {
    // pub fn get_nature_crop_model(
    //     &self,
    //     name: &str,
    //     assets_mesh: &Res<Assets<Mesh>>,
    // ) -> Option<Mesh> {
    //     let input_name = name.replace(".gltf", "");
    //     let path = format!("models/nature_crops/{}.gltf", input_name);
    //     self.nature_crops
    //         .get(path.as_str())
    //         .and_then(|handle| assets_mesh.get(handle).cloned())
    // }

    pub fn get_nature_stylized_model(
        &self,
        name: &str,
        assets_gltf: &Res<Assets<Gltf>>,
    ) -> Option<SceneRoot> {
        let input_name = name.replace(".gltf", "");
        let path = format!("models/nature_stylized/{}.gltf", input_name);
        self.nature_stylized.get(path.as_str()).and_then(|handle| {
            let Some(gltf) = assets_gltf.get(handle) else {
                return None;
            };

            Some(SceneRoot(gltf.scenes[0].clone()))
        })
    }
}
