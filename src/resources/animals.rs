use bevy::asset::{Assets, Handle};
use bevy::gltf::Gltf;
use bevy::platform::collections::HashMap;
use bevy::prelude::{Res, Resource};
use bevy_asset_loader::asset_collection::AssetCollection;
use bevy_gltf_animation::prelude::GltfSceneRoot;

#[derive(AssetCollection, Resource, Debug)]
pub struct AnimalsAssets {
    #[asset(path = "models/animals", collection(mapped, typed))]
    pub models: HashMap<String, Handle<Gltf>>,
}

#[allow(non_snake_case, non_upper_case_globals)]
pub mod AnimalsAssetsKeys {
    pub const BEE: &'static str = "Bee";
    pub const BEE_HIVE: &'static str = "Bee_Hive";
    pub const PUDU: &'static str = "Pudu_Animations";
    pub const DEER: &'static str = "Deer_Animations";
}

impl AnimalsAssets {
    pub fn get_model(
        &self,
        name: &str,
        assets_gltf: &Res<Assets<Gltf>>,
    ) -> Option<GltfSceneRoot> {
        let input_name = name.replace(".gltf", "");
        let path = format!("models/animals/{}.gltf", input_name);

        let model_handle = self
            .models
            .get(path.as_str())
            .or_else(|| self.models.get(path.replace(".gltf", ".glb").as_str()));

        model_handle.and_then(|handle| {
            // let Some(gltf) = assets_gltf.get(handle) else {
            //     return None;
            // };

            // Some(SceneRoot(gltf.scenes[0].clone()))
            Some(GltfSceneRoot::new(handle.clone()))
        })
    }
}
