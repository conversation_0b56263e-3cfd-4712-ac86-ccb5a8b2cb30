use crate::components::environment::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TerrainPod};
use crate::components::GameState;
use crate::plugins::plants_plugin::PlantsPluginState;
use crate::resources::animals::AnimalsAssets;
use crate::resources::environment::TerrainPodGrid;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use crate::systems::environment::system_params::TerrainSystemParams;
use crate::systems::plants::spawn_plants;
use bevy::prelude::*;

pub struct WorldGenerationPlugin;

#[derive(Debug, <PERSON><PERSON>, Copy, Eq, PartialEq, Hash)]
struct InitialStateReady;

#[derive(<PERSON><PERSON>, Eq, PartialEq, Debug, <PERSON>h, <PERSON><PERSON>ult, States)]
enum TerrainGenerationState {
    #[default]
    Init,
    Done,
}

impl ComputedStates for InitialStateReady {
    type SourceStates = (GameState, PlantsPluginState);

    fn compute(sources: Self::SourceStates) -> Option<Self> {
        match sources {
            (GameState::Ready, PlantsPluginState::Done) => Some(Self),
            _ => None,
        }
    }
}

impl Plugin for WorldGenerationPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<TerrainGenerationState>()
            .add_computed_state::<InitialStateReady>()
            .add_systems(
                OnEnter(InitialStateReady),
                (generate_terrain, crate::systems::environment::setup_sun),
            )
            .add_systems(
                Update,
                spawn_animals
                    .after(generate_terrain)
                    .run_if(resource_exists::<TerrainPodGrid>)
                    .run_if(resource_exists::<AnimalsAssets>)
                    .run_if(resource_exists::<Assets<Gltf>>)
                    .run_if(in_state(TerrainGenerationState::Done)),
            )
            .add_systems(
                Update,
                spawn_plants
                    .after(generate_terrain)
                    .run_if(resource_exists::<TerrainPodGrid>)
                    .run_if(resource_exists::<AnimalsAssets>)
                    .run_if(in_state(TerrainGenerationState::Done)),
            );
    }
}

fn spawn_animals(
    mut commands: Commands,
    mut animals_assets: AnimalsAssetSystemParams,
    mut spawned: Local<bool>,
) {
    if *spawned {
        return;
    }

    crate::behaviors::spawn_deer_factory(&mut commands, &mut animals_assets);
    *spawned = true;
}

fn generate_terrain(
    mut commands: Commands,
    mut terrain_params: TerrainSystemParams,
    mut app_next_state: ResMut<NextState<TerrainGenerationState>>,
) {
    crate::systems::environment::terrain_generation::generate_terrain_world(
        &mut commands,
        &mut terrain_params,
        crate::systems::environment::TerrainGenerationParams {
            pod_width: 5,
            pod_depth: 5,
            elevation_scale: 0.0,
            cell_size: 0.5,
            ..Default::default()
        },
        (1, 0),
    );

    log::info!("Terrain generation complete");
    app_next_state.set(TerrainGenerationState::Done);
}
