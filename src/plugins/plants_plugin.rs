use crate::components::plants::lifecycle::PlantLifecycle;
use crate::components::plants::photosynthesis::{EnergyConsumption, LightAbsorption};
use crate::components::plants::pollination::{PhysicalPollen, Pollen, PollinationStatus};
use crate::components::plants::{
    NectarProduction, PlantAge, PlantGrowthStage, PlantName, PlantPollinationType, PlantProperties, PlantSpeciesName
};
use crate::events::plants_events::{PlantDied, PlantGrowthChange, PlantPollinated};
use crate::resources::plants::{
    ModelPattern, PlantAssets, PlantModelData, PlantModelType, PlantSpeciesAsset, PlantSpeciesLoadedFolder
};
use crate::systems::plants::*;
use bevy::asset::LoadedFolder;
use bevy::prelude::*;
use bevy_asset_loader::prelude::*;
use bevy_common_assets::ron::RonAssetPlugin;

pub struct PlantsPlugin;

#[derive(<PERSON><PERSON>, <PERSON>q, PartialEq, Debug, Hash, Default, States)]
pub enum PlantsPluginState {
    #[default]
    LoadingImages,
    LoadingAssets,
    LoadingData,
    Done,
}

impl Plugin for PlantsPlugin {
    fn build(&self, app: &mut App) {
        app.add_plugins(RonAssetPlugin::<PlantSpeciesAsset>::new(&["plant.species.ron"]))
            .init_state::<PlantsPluginState>()
            .add_event::<PlantGrowthChange>()
            .add_event::<PlantDied>()
            .add_event::<PlantPollinated>()
            .register_type::<PlantGrowthStage>()
            .register_type::<PlantAge>()
            .register_type::<LightAbsorption>()
            .register_type::<EnergyConsumption>()
            .register_type::<PhysicalPollen>()
            .register_type::<PlantSpeciesName>()
            .register_type::<PlantName>()
            .register_type::<PlantProperties>()
            .register_type::<Pollen>()
            .register_type::<PollinationStatus>()
            .register_type::<PlantPollinationType>()
            .register_type::<NectarProduction>()
            .register_type::<PlantLifecycle>()
            .register_type::<PlantModelType>()
            .register_type::<PlantModelData>()
            .register_type::<ModelPattern>()
            .add_loading_state(
                LoadingState::new(PlantsPluginState::LoadingImages)
                    .continue_to_state(PlantsPluginState::LoadingAssets)
                    .load_collection::<PlantAssets>(),
            )
            .add_systems(OnEnter(PlantsPluginState::LoadingAssets), load_folders)
            .add_systems(
                Update,
                setup
                    .run_if(resource_exists::<PlantSpeciesLoadedFolder>)
                    .run_if(in_state(PlantsPluginState::LoadingAssets)),
            )
            .add_systems(
                Update,
                // Render
                (
                    render::set_spawned_tree_materials
                        .before(crate::systems::render::set_pixelated_materials),
                    render::render_spawned_plants,
                    render::update_plant_sprites,
                    render::render_pollen,
                )
                    .run_if(in_state(PlantsPluginState::Done)),
            )
            .add_systems(
                FixedUpdate,
                (
                    // Lifecycle
                    lifecycle::update_plant_growth_stages,
                    // Growth
                    update_plant_growth,
                    apply_genetics_to_plant_growth,
                    handle_reproduction,
                    // Photosynthesis
                    photosynthesis::calculate_light_absorption,
                    photosynthesis::perform_photosynthesis,
                    photosynthesis::apply_energy_to_growth,
                    // Pollination
                    pollination::update_pollination_status,
                    pollination::release_pollen,
                    pollination::update_pollen_lifetime,
                    pollination::natural_wind_pollination,
                    pollination::animal_pollination,
                    // Nectar production
                    nectar::update_nectar_production,
                )
                    .run_if(in_state(PlantsPluginState::Done)),
            );
    }
}

fn load_folders(mut commands: Commands, asset_server: Res<AssetServer>) {
    let loaded_folder = asset_server.load_folder("data/plants");
    commands.insert_resource(PlantSpeciesLoadedFolder(loaded_folder));
}

fn setup(
    loaded_folders: Res<Assets<LoadedFolder>>,
    loaded_folder: Res<PlantSpeciesLoadedFolder>,
    asset_server: Res<AssetServer>,
    mut plant_assets: ResMut<PlantAssets>,
    mut next_state: ResMut<NextState<PlantsPluginState>>,
) {
    let is_loaded = asset_server
        .get_load_state(loaded_folder.0.id())
        .is_some_and(|s| s.is_loaded());

    if !is_loaded {
        return;
    }

    if let Some(folder) = loaded_folders.get(&loaded_folder.0) {
        for handle in &folder.handles {
            let typed_handle = handle.clone().typed::<PlantSpeciesAsset>();

            if let Some(path) = handle.path() {
                if let Some(stem) = path.path().file_stem() {
                    if let Some(stem_str) = stem.to_str() {
                        let key = stem_str.split('.').next().unwrap_or(stem_str);
                        plant_assets.species.insert(key.to_string(), typed_handle);
                    }
                }
            }
        }

        log::info!("Finished loading plant species data.");
        next_state.set(PlantsPluginState::Done);
    }
}

// fn check_assets(
//     mut app_next_state: ResMut<NextState<PlantsPluginState>>,
//     mut events: EventReader<AssetEvent<LoadedFolder>>,
// ) {
//     for event in events.read() {
//         if let AssetEvent::LoadedWithDependencies { id: _ } = event {
//             // println!("asset loaded!");
//             // app_next_state.set(AssetsState::InGame);
//         }
//     }
// }
