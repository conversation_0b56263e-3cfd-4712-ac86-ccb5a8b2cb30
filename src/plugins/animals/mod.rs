use crate::behaviors::{BeeB<PERSON><PERSON>orsPlugin, CommonBehaviorsPlugin, DeerBehaviorsPlugin};
use crate::components::animals::{AnimalQuadrupedBaseArmature, MovementDampingFactor};
use crate::resources::animals::AnimalsAssets;
use crate::systems::animals::{
    apply_movement_damping, auto_rig_colliders, on_added_gltf_scene
};
use bevy::prelude::*;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::ConfigureLoadingState;
use bevy_descendant_collector::{DescendantCollectorPlugin, HierarchyRootPosition};

#[derive(<PERSON>lone, Eq, PartialEq, Debug, <PERSON>h, De<PERSON>ult, States)]
pub enum AnimalsPluginState {
    #[default]
    LoadingAssets,
    Done,
}

pub struct AnimalsPlugin;

impl Plugin for AnimalsPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<AnimalsPluginState>()
            .add_loading_state(
                LoadingState::new(AnimalsPluginState::LoadingAssets)
                    .continue_to_state(AnimalsPluginState::Done)
                    .load_collection::<AnimalsAssets>(),
            )
            .add_plugins(DescendantCollectorPlugin::<AnimalQuadrupedBaseArmature>::new(
                HierarchyRootPosition::Scene,
            ))
            .register_type::<MovementDampingFactor>()
            .register_type::<AnimalQuadrupedBaseArmature>()
            .add_systems(Update, on_added_gltf_scene::<AnimalQuadrupedBaseArmature>)
            .add_systems(Update, auto_rig_colliders::<AnimalQuadrupedBaseArmature>)
            .add_systems(Update, apply_movement_damping)
            .add_plugins(AnimalBehaviorsPlugin);
    }
}

pub struct AnimalBehaviorsPlugin;

impl Plugin for AnimalBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app
            // Add common behaviors that can be used by any animal
            .add_plugins(CommonBehaviorsPlugin)
            .add_plugins(DeerBehaviorsPlugin)
            .add_plugins(BeeBehaviorsPlugin);
    }
}
