[package]
name = "project_gaia_forge"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A project focused on ecological solutions"
readme = "README.md"
repository = "https://github.com/PianoRhythmOak/project_gaia_forge"
license = "MIT"
rust-version = "1.86"

[features]
default = []

[dependencies]
bevy = { version = "0.16.0", features = ["bevy_dev_tools", "file_watcher", "track_location"] }
bevy_common_assets = { version = "0.13.0", features = ["ron", "json"] }
bevy_sprite3d = "5.0.0"
bevy-inspector-egui = "0.31.0"
bevy_hanabi = { git = "https://github.com/djeedai/bevy_hanabi", branch = "main", default-features = false, features = ["3d"] }
bevy_behave = "0.3.0"
bevy_gltf_animation = "0.1.1"
bevy_asset_loader = "0.23.0-rc.3"
smooth-bevy-cameras = "0.14.0"
bevy_tween = "0.8.0"
bevy_shader_utils = { git = "https://github.com/rust-adventure/bevy-examples", branch = "main" }
bevy_obj = "0.16"
bevy_descendant_collector = "0.4.0"
avian3d = { git = "https://github.com/Jondolf/avian", branch = "main" }
log = { version = "*", features = [
    #    "max_level_debug",
    "release_max_level_warn"
] }
serde_with = "3.12.0"
rand = "0.8.5"
noise = "0.9.0"
serde = { version = "1.0.219", features = ["derive"] }
big-brain = { git = "https://github.com/zkat/big-brain", branch = "main" }
bitflags = "2.9.0"
smallvec = "1.15.0"
neuroflow = "0.2.0"

[dev-dependencies]
bevy = { version = "0.16.0", default-features = false }

[dependencies.uuid]
version = "1.12.0"
features = [
    "v4",
    "serde"
]

# Enable a small amount of optimization in debug mode
[profile.dev]
opt-level = 1

# Enable high optimizations for dependencies (incl. Bevy), but not for our code:
[profile.dev.package."*"]
opt-level = 3

[profile.release]
opt-level = 's'
lto = 'thin'
codegen-units = 1

[profile.wasm-release]
inherits = "release"
opt-level = "s"
lto = 'thin'
codegen-units = 1
